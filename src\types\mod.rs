//! Data structures and types

pub mod events;
pub mod validation;

use serde::{Deserialize, Serialize};
use std::time::SystemTime;

/// Phone number wrapper with validation following E.164 format
///
/// # Examples
/// ```
/// use whatsmeow_rs::types::PhoneNumber;
///
/// let phone = PhoneNumber::new("+1234567890").unwrap();
/// assert_eq!(phone.as_str(), "+1234567890");
/// ```
#[derive(Debug, <PERSON>lone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub struct PhoneNumber(String);

impl PhoneNumber {
    /// Create a new phone number with validation
    pub fn new(number: impl Into<String>) -> Result<Self, crate::error::WhatsAppError> {
        let number = number.into();
        if Self::is_valid(&number) {
            Ok(PhoneNumber(number))
        } else {
            Err(crate::error::WhatsAppError::InvalidInput(format!(
                "Invalid phone number format: {}. Must be E.164 format: + followed by 7-15 digits",
                number
            )))
        }
    }

    /// Try to create a phone number, returning None if invalid
    pub fn try_new(number: impl Into<String>) -> Option<Self> {
        Self::new(number).ok()
    }

    /// Check if phone number format is valid according to E.164 standard
    fn is_valid(number: &str) -> bool {
        validation::validate_phone_number(number).is_ok()
    }

    /// Get the phone number as string
    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Extract country code from phone number (first 1-4 digits after +)
    pub fn country_code(&self) -> Option<&str> {
        // Most country codes are 1-4 digits
        let digits = &self.0[1..];
        for len in 1..=4.min(digits.len()) {
            if self.is_valid_country_code(&digits[..len]) {
                return Some(&digits[..len]);
            }
        }
        None
    }

    /// Check if a string represents a valid country code
    fn is_valid_country_code(&self, code: &str) -> bool {
        // Basic validation - in a real implementation, you'd check against
        // a list of valid ITU-T country codes
        matches!(code.len(), 1..=4) && code.chars().all(|c| c.is_ascii_digit())
    }

    /// Get the national number (without country code)
    pub fn national_number(&self) -> Option<&str> {
        if let Some(country_code) = self.country_code() {
            Some(&self.0[1 + country_code.len()..])
        } else {
            None
        }
    }
}

impl std::fmt::Display for PhoneNumber {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        write!(f, "{}", self.0)
    }
}

/// Core message types
#[derive(Debug, Clone, Serialize, Deserialize)]
pub enum Message {
    Text {
        to: PhoneNumber,
        content: String,
    },
    Image {
        to: PhoneNumber,
        data: Vec<u8>,
        caption: Option<String>,
        mime_type: String,
    },
}

impl Message {
    /// Create a text message builder
    pub fn text() -> TextMessageBuilder {
        TextMessageBuilder::new()
    }

    /// Create an image message builder
    pub fn image() -> ImageMessageBuilder {
        ImageMessageBuilder::new()
    }

    /// Quick constructor for text messages
    pub fn new_text(
        to: PhoneNumber,
        content: impl Into<String>,
    ) -> Result<Self, crate::error::WhatsAppError> {
        Self::text().to(to).content(content).build()
    }

    /// Quick constructor for image messages
    pub fn new_image(
        to: PhoneNumber,
        data: Vec<u8>,
        mime_type: impl Into<String>,
    ) -> Result<Self, crate::error::WhatsAppError> {
        Self::image().to(to).data(data).mime_type(mime_type).build()
    }

    /// Get the recipient of this message
    pub fn recipient(&self) -> &PhoneNumber {
        match self {
            Message::Text { to, .. } => to,
            Message::Image { to, .. } => to,
        }
    }

    /// Validate message content
    pub fn validate(&self) -> Result<(), crate::error::WhatsAppError> {
        match self {
            Message::Text { content, .. } => validation::validate_text_content(content),
            Message::Image {
                data, mime_type, ..
            } => validation::validate_image_data(data, mime_type),
        }
    }
}

/// Builder for text messages
#[derive(Debug, Default)]
pub struct TextMessageBuilder {
    to: Option<PhoneNumber>,
    content: Option<String>,
}

impl TextMessageBuilder {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn to(mut self, recipient: PhoneNumber) -> Self {
        self.to = Some(recipient);
        self
    }

    pub fn content(mut self, text: impl Into<String>) -> Self {
        self.content = Some(text.into());
        self
    }

    pub fn build(self) -> Result<Message, crate::error::WhatsAppError> {
        let to = self.to.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("Recipient is required".to_string())
        })?;

        let content = self.content.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("Message content is required".to_string())
        })?;

        let message = Message::Text { to, content };
        message.validate()?;
        Ok(message)
    }
}

/// Builder for image messages
#[derive(Debug, Default)]
pub struct ImageMessageBuilder {
    to: Option<PhoneNumber>,
    data: Option<Vec<u8>>,
    caption: Option<String>,
    mime_type: Option<String>,
}

impl ImageMessageBuilder {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn to(mut self, recipient: PhoneNumber) -> Self {
        self.to = Some(recipient);
        self
    }

    pub fn data(mut self, image_data: Vec<u8>) -> Self {
        self.data = Some(image_data);
        self
    }

    pub fn caption<T: Into<String>>(mut self, caption: T) -> Self {
        self.caption = Some(caption.into());
        self
    }

    pub fn mime_type<T: Into<String>>(mut self, mime_type: T) -> Self {
        self.mime_type = Some(mime_type.into());
        self
    }

    /// Convenience method for JPEG images
    pub fn jpeg(self, image_data: Vec<u8>) -> Self {
        self.data(image_data).mime_type("image/jpeg")
    }

    /// Convenience method for PNG images
    pub fn png(self, image_data: Vec<u8>) -> Self {
        self.data(image_data).mime_type("image/png")
    }

    pub fn build(self) -> Result<Message, crate::error::WhatsAppError> {
        let to = self.to.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("Recipient is required".to_string())
        })?;

        let data = self.data.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("Image data is required".to_string())
        })?;

        let mime_type = self.mime_type.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("MIME type is required".to_string())
        })?;

        let message = Message::Image {
            to,
            data,
            caption: self.caption,
            mime_type,
        };

        message.validate()?;
        Ok(message)
    }
}

/// Event types for the WhatsApp client
#[derive(Debug, Clone)]
pub enum Event {
    MessageReceived(IncomingMessage),
    ConnectionStatusChanged(ConnectionStatus),
    QRCodeGenerated(QRCodeData),
    LoginSuccess,
    LoginFailure(LoginError),
}

/// QR code data with metadata
#[derive(Debug, Clone, PartialEq)]
pub struct QRCodeData {
    pub data: String,
    pub expires_at: SystemTime,
    pub attempt_count: u32,
}

/// Login error with detailed information
#[derive(Debug, Clone)]
pub struct LoginError {
    pub reason: LoginFailureReason,
    pub message: String,
    pub retry_after: Option<std::time::Duration>,
}

/// Specific login failure reasons
#[derive(Debug, Clone, PartialEq)]
pub enum LoginFailureReason {
    QRCodeExpired,
    QRCodeRejected,
    NetworkError,
    AuthenticationTimeout,
    InvalidCredentials,
    RateLimited,
}

/// Incoming message structure
#[derive(Debug, Clone)]
pub struct IncomingMessage {
    pub from: String,
    pub content: MessageContent,
    pub timestamp: SystemTime,
    pub message_id: Option<String>,
}

/// Message content types
#[derive(Debug, Clone)]
pub enum MessageContent {
    Text(String),
    Image {
        data: Vec<u8>,
        caption: Option<String>,
    },
}

/// Connection status
#[derive(Debug, Clone)]
pub enum ConnectionStatus {
    Connected,
    Disconnected,
    Connecting,
    Reconnecting,
}

/// Message delivery status information
#[derive(Debug, Clone)]
pub struct MessageDeliveryStatus {
    pub message_id: String,
    pub recipient: PhoneNumber,
    pub status: DeliveryStatus,
    pub timestamp: SystemTime,
    pub error: Option<String>,
}

/// Delivery status enumeration matching WhatsApp protocol
#[derive(Debug, Clone, PartialEq)]
pub enum DeliveryStatus {
    /// Message failed to send
    Failed,
    /// Message is pending delivery
    Pending,
    /// Message was sent to server
    Sent,
    /// Server acknowledged receipt
    ServerAck,
    /// Message was delivered to recipient
    Delivered,
    /// Delivery acknowledged by recipient's device
    DeliveryAck,
    /// Message was read by recipient
    Read,
    /// Message was played (for voice messages)
    Played,
    /// Generic error state
    Error,
}
