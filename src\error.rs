//! Error types for the WhatsApp client

use thiserror::Error;

/// Main error type for WhatsApp operations
#[derive(Debug, Error)]
pub enum WhatsAppError {
    #[error("Connection error: {0}")]
    Connection(#[from] tokio_tungstenite::tungstenite::Error),

    #[error("Authentication failed: {0}")]
    Authentication(String),

    #[error("Protocol error: {0}")]
    Protocol(String),

    #[error("Encryption error: {0}")]
    Encryption(String),

    #[error("Serialization error: {0}")]
    Serialization(#[from] prost::DecodeError),

    #[error("Encoding error: {0}")]
    Encoding(#[from] prost::EncodeError),

    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),

    #[error("Session error: {0}")]
    Session(String),

    #[error("QR code generation failed: {0}")]
    QrCode(String),

    #[error("Timeout occurred after {timeout:?}")]
    Timeout { timeout: std::time::Duration },

    #[error("Invalid configuration: {0}")]
    InvalidConfig(String),

    #[error("Client not connected")]
    NotConnected,

    #[error("Client already connected")]
    AlreadyConnected,

    #[error("Invalid input: {0}")]
    InvalidInput(String),

    #[error("Operation '{operation}' failed: {context}")]
    Operation {
        operation: String,
        context: String,
        #[source]
        source: Box<WhatsAppError>,
    },

    #[error("Generic error: {0}")]
    Generic(String),
}

/// Convenience Result type
pub type Result<T> = std::result::Result<T, WhatsAppError>;
impl WhatsAppError {
    /// Create a new operation error with context
    pub fn operation(operation: impl Into<String>, context: impl Into<String>, source: WhatsAppError) -> Self {
        Self::Operation {
            operation: operation.into(),
            context: context.into(),
            source: Box::new(source),
        }
    }

    /// Check if this error is related to authentication
    pub fn is_auth_error(&self) -> bool {
        matches!(self, WhatsAppError::Authentication(_))
    }

    /// Check if this error is related to connection issues
    pub fn is_connection_error(&self) -> bool {
        matches!(self, WhatsAppError::Connection(_) | WhatsAppError::NotConnected | WhatsAppError::AlreadyConnected)
    }

    /// Check if this error is recoverable (can retry the operation)
    pub fn is_recoverable(&self) -> bool {
        matches!(
            self,
            WhatsAppError::Connection(_) 
            | WhatsAppError::Timeout { .. } 
            | WhatsAppError::NotConnected
        )
    }
}