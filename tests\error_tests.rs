use std::sync::Arc;
use whatsmeow_rs::types::QRCodeData;
use whatsmeow_rs::*;

/// Test error types
#[test]
fn test_error_types() {
    let auth_error = WhatsAppError::Authentication("Invalid credentials".to_string());
    assert!(matches!(auth_error, WhatsAppError::Authentication(_)));

    let connection_error = WhatsAppError::NotConnected;
    assert!(matches!(connection_error, WhatsAppError::NotConnected));

    let config_error = WhatsAppError::InvalidConfig("Bad config".to_string());
    assert!(matches!(config_error, WhatsAppError::InvalidConfig(_)));
}

/// Test event types
#[test]
fn test_event_types() {
    // QR Code event
    let qr_event = Event::QRCodeGenerated(QRCodeData {
        data: "test_qr_data".to_string(),
        expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(300),
        attempt_count: 1,
    });
    match qr_event {
        Event::QRCodeGenerated(data) => assert_eq!(data.data, "test_qr_data"),
        _ => panic!("Expected QR code event"),
    }

    // Login success event
    let login_event = Event::LoginSuccess;
    assert!(matches!(login_event, Event::LoginSuccess));

    // Connection status event
    let conn_event = Event::ConnectionStatusChanged(ConnectionStatus::Connected);
    match conn_event {
        Event::ConnectionStatusChanged(status) => {
            assert!(matches!(status, ConnectionStatus::Connected));
        }
        _ => panic!("Expected connection status event"),
    }
}

/// Mock event handler for testing
struct MockEventHandler;

#[async_trait::async_trait]
impl EventHandler for MockEventHandler {
    async fn handle_event(&self, _event: Event) -> Result<()> {
        Ok(())
    }
}

/// Test event handler integration
#[tokio::test]
async fn test_event_handler() {
    let mut client = WhatsAppClient::new().await.unwrap();
    let handler = Arc::new(MockEventHandler);

    // This should not panic
    client.set_event_handler(handler);

    // Test event subscription
    let _receiver = client.subscribe_to_events();
}
