//! # WhatsApp Web Client Library for Rust
//!
//! This library provides a Rust implementation of a WhatsApp Web client,
//! similar to the Go whatsmeow library. It enables developers to build
//! applications that can connect to WhatsApp Web, authenticate via QR code,
//! and send/receive messages programmatically.
//!
//! ## Quick Start
//!
//! ```rust,no_run
//! use whatsmeow_rs::{WhatsAppClient, PhoneNumber, Result};
//!
//! #[tokio::main]
//! async fn main() -> Result<()> {
//!     // Create and configure client
//!     let mut client = WhatsAppClient::new().await?;
//!     
//!     // Connect to WhatsApp servers
//!     client.connect().await?;
//!     
//!     // Authenticate (displays QR code in terminal)
//!     client.login().await?;
//!     
//!     // Send a message
//!     let recipient = PhoneNumber::new("+1234567890")?;
//!     let delivery_status = client
//!         .send_message(recipient, "Hello from Rust!")
//!         .await?;
//!     
//!     println!("Message sent with ID: {}", delivery_status.message_id);
//!     
//!     // Clean shutdown
//!     client.disconnect().await?;
//!     Ok(())
//! }
//! ```
//!
//! ## Documentation
//!
//! For detailed examples and usage patterns, see:
//! - [Quick Start Guide](https://github.com/your-repo/whatsmeow-rs/blob/main/docs/quick-start.md)
//! - [Advanced Usage](https://github.com/your-repo/whatsmeow-rs/blob/main/docs/advanced.md) 
//! - [Error Handling](https://github.com/your-repo/whatsmeow-rs/blob/main/docs/error-handling.md)
//! - [Features](https://github.com/your-repo/whatsmeow-rs/blob/main/docs/features.md)
//!
//! ## Features
//!
//! - **Async/await support** - Built on tokio for high-performance async operations
//! - **Session persistence** - Automatically save and restore authentication sessions
//! - **QR code authentication** - Terminal-based QR code display for easy pairing
//! - **Message sending** - Send text and image messages with delivery tracking
//! - **Event-driven architecture** - Handle incoming messages and connection events
//! - **Type-safe phone numbers** - Validated phone number handling with E.164 format
//! - **Automatic reconnection** - Robust connection handling with exponential backoff
//! - **End-to-end encryption** - Full WhatsApp protocol encryption support

// Core modules
pub mod auth;
pub mod client;
pub mod config;
pub mod error;
pub mod protocol;
pub mod types;

// Client and builder
pub use client::{WhatsAppClient, ClientBuilder, EventHandler, ConnectionState, SessionInfo};

// Core messaging types
pub use types::{
    Message, MessageContent, IncomingMessage, 
    PhoneNumber, MessageDeliveryStatus, DeliveryStatus,
    TextMessageBuilder, ImageMessageBuilder
};

// Events and status
pub use types::{Event, ConnectionStatus, QRCodeData, LoginError, LoginFailureReason};

// Error handling
pub use error::{Result, WhatsAppError};

// Authentication (for advanced usage)
pub use auth::{PairingManager, QRGenerator};

// Protocol types (for advanced usage)
pub use protocol::proto::{
    MessageKey,
    Message as ProtoMessage,
};


