//! Optimized operations to reduce lock contention

use crate::error::Result;
use crate::client::session::Session;
use std::sync::Arc;
use tokio::sync::Mutex;

/// Helper to perform session operations with minimal lock time
pub struct SessionOperations;

impl SessionOperations {
    /// Check session validity without holding lock for long
    pub async fn is_valid_quick(session: &Arc<Mutex<Session>>) -> bool {
        // Quick check - acquire lock, check, release immediately
        let session_guard = session.lock().await;
        let is_valid = session_guard.is_valid();
        drop(session_guard); // Explicit drop to show intent
        is_valid
    }

    /// Get session info with minimal lock time
    pub async fn get_info_quick(session: &Arc<Mutex<Session>>) -> Option<SessionInfo> {
        let session_guard = session.lock().await;
        if !session_guard.is_valid() {
            return None;
        }

        // Extract data quickly
        let info = SessionInfo {
            device_id: session_guard.device_id.clone(),
            is_authenticated: session_guard.is_authenticated,
            last_seen: session_guard.last_seen,
            created_at: session_guard.created_at,
            age_seconds: session_guard.age_seconds(),
            needs_refresh: session_guard.needs_refresh(),
        };
        drop(session_guard);
        
        Some(info)
    }

    /// Update session with batched operations
    pub async fn update_batch<F>(session: &Arc<Mutex<Session>>, updater: F) -> Result<()>
    where
        F: FnOnce(&mut Session) -> Result<()>,
    {
        let mut session_guard = session.lock().await;
        updater(&mut *session_guard)?;
        drop(session_guard);
        Ok(())
    }
}

/// Session information for client status
#[derive(Debug, Clone)]
pub struct SessionInfo {
    pub device_id: String,
    pub is_authenticated: bool,
    pub last_seen: chrono::DateTime<chrono::Utc>,
    pub created_at: chrono::DateTime<chrono::Utc>,
    pub age_seconds: Option<u64>,
    pub needs_refresh: bool,
}

/// Connection operations with reduced lock contention
pub struct ConnectionOperations;

impl ConnectionOperations {
    /// Perform multiple connection operations atomically
    pub async fn batch_operations<T, F, Fut>(
        connection: &Arc<Mutex<T>>,
        operations: F,
    ) -> Result<()>
    where
        F: FnOnce(&mut T) -> Fut,
        Fut: std::future::Future<Output = Result<()>>,
    {
        let mut conn_guard = connection.lock().await;
        let result = operations(&mut *conn_guard).await;
        drop(conn_guard);
        result
    }
}