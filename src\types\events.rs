//! Event handling types

use super::{ConnectionStatus, Event, IncomingMessage, QRCodeData, LoginError};

/// Event handler trait for processing WhatsApp events
pub trait EventProcessor {
    /// Process an incoming event
    fn process_event(&self, event: Event);

    /// Handle message received event
    fn on_message_received(&self, _message: IncomingMessage) {}

    /// Handle connection status change
    fn on_connection_status_changed(&self, _status: ConnectionStatus) {}

    /// Handle QR code generation
    fn on_qr_code_generated(&self, _qr_data: QRCodeData) {}

    /// Handle successful login
    fn on_login_success(&self) {}

    /// Handle login failure
    fn on_login_failure(&self, _error: LoginError) {}
}

/// Default implementation that routes events to specific handlers
impl<T: EventProcessor> EventProcessor for T {
    fn process_event(&self, event: Event) {
        match event {
            Event::MessageReceived(msg) => self.on_message_received(msg),
            Event::ConnectionStatusChanged(status) => self.on_connection_status_changed(status),
            Event::QRCodeGenerated(qr_data) => self.on_qr_code_generated(qr_data),
            Event::LoginSuccess => self.on_login_success(),
            Event::LoginFailure(error) => self.on_login_failure(error),
        }
    }
}
