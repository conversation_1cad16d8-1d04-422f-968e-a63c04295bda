# Design Document

## Overview

This design outlines the implementation of a Rust equivalent to the Go whatsmeow library for connecting to WhatsApp Web. The system will be built using modern Rust async patterns and will provide a clean, modular architecture for WhatsApp Web client functionality.

The core design follows WhatsApp Web's protocol which uses:
- WebSocket connections for real-time communication
- Protocol Buffers for message serialization
- Signal Protocol for end-to-end encryption
- Noise Protocol for initial handshake encryption
- QR code-based authentication for device pairing

## Architecture

The system will be organized into several key modules:

```
whatsmeow-rs/
├── src/
│   ├── lib.rs              # Main library entry point
│   ├── client/             # Main client implementation
│   │   ├── mod.rs
│   │   ├── connection.rs   # WebSocket connection management
│   │   └── session.rs      # Session state management
│   ├── auth/               # Authentication handling
│   │   ├── mod.rs
│   │   ├── qr.rs          # QR code generation and handling
│   │   └── pairing.rs     # Device pairing logic
│   ├── protocol/           # Protocol implementation
│   │   ├── mod.rs
│   │   ├── messages.rs    # Message handling
│   │   └── crypto.rs      # Encryption/decryption
│   ├── types/              # Data structures and types
│   │   ├── mod.rs
│   │   └── events.rs      # Event types
│   └── error.rs           # Error handling
```

## Components and Interfaces

### 1. Client Module

**WhatsAppClient**: Main client struct that orchestrates all operations
```rust
pub struct WhatsAppClient {
    connection: Arc<Mutex<Connection>>,
    session: Arc<Mutex<Session>>,
    event_handler: Option<Box<dyn EventHandler>>,
}

impl WhatsAppClient {
    pub async fn new() -> Result<Self, WhatsAppError>;
    pub async fn connect(&mut self) -> Result<(), WhatsAppError>;
    pub async fn login(&mut self) -> Result<LoginResult, WhatsAppError>;
    pub async fn send_message(&self, to: &str, text: &str) -> Result<(), WhatsAppError>;
    pub async fn disconnect(&mut self) -> Result<(), WhatsAppError>;
}
```

**Connection**: Manages WebSocket connection and low-level protocol
```rust
pub struct Connection {
    websocket: Option<WebSocketStream<MaybeTlsStream<TcpStream>>>,
    url: String,
    reconnect_attempts: u32,
}

impl Connection {
    pub async fn connect(&mut self) -> Result<(), WhatsAppError>;
    pub async fn send_frame(&mut self, data: Vec<u8>) -> Result<(), WhatsAppError>;
    pub async fn receive_frame(&mut self) -> Result<Vec<u8>, WhatsAppError>;
    pub async fn close(&mut self) -> Result<(), WhatsAppError>;
}
```

### 2. Authentication Module

**QRGenerator**: Handles QR code generation and display
```rust
pub struct QRGenerator {
    pub ref_id: String,
    pub public_key: Vec<u8>,
    pub private_key: Vec<u8>,
}

impl QRGenerator {
    pub fn new() -> Self;
    pub fn generate_qr_data(&self) -> String;
    pub fn display_qr(&self) -> Result<(), WhatsAppError>;
}
```

**PairingManager**: Manages device pairing process
```rust
pub struct PairingManager {
    noise_state: NoiseState,
    client_id: String,
}

impl PairingManager {
    pub async fn initiate_pairing(&mut self) -> Result<QRGenerator, WhatsAppError>;
    pub async fn complete_pairing(&mut self, scan_result: Vec<u8>) -> Result<Session, WhatsAppError>;
}
```

### 3. Protocol Module

**MessageHandler**: Handles Protocol Buffer message serialization/deserialization
```rust
pub struct MessageHandler {
    encryption: EncryptionManager,
}

impl MessageHandler {
    pub fn serialize_message(&self, msg: &Message) -> Result<Vec<u8>, WhatsAppError>;
    pub fn deserialize_message(&self, data: &[u8]) -> Result<Message, WhatsAppError>;
    pub fn handle_incoming_message(&self, data: Vec<u8>) -> Result<Event, WhatsAppError>;
}
```

**EncryptionManager**: Handles all cryptographic operations
```rust
pub struct EncryptionManager {
    noise_state: Option<NoiseState>,
    signal_store: SignalStore,
}

impl EncryptionManager {
    pub fn encrypt_message(&self, plaintext: &[u8]) -> Result<Vec<u8>, WhatsAppError>;
    pub fn decrypt_message(&self, ciphertext: &[u8]) -> Result<Vec<u8>, WhatsAppError>;
    pub fn initialize_noise_protocol(&mut self) -> Result<(), WhatsAppError>;
}
```

### 4. Session Management

**Session**: Maintains connection state and credentials
```rust
pub struct Session {
    pub device_id: String,
    pub client_token: String,
    pub server_token: String,
    pub encryption_keys: EncryptionKeys,
    pub last_seen: SystemTime,
}

impl Session {
    pub fn new() -> Self;
    pub fn save_to_file(&self, path: &Path) -> Result<(), WhatsAppError>;
    pub fn load_from_file(path: &Path) -> Result<Self, WhatsAppError>;
    pub fn is_valid(&self) -> bool;
}
```

## Data Models

### Core Message Types
```rust
#[derive(Debug, Clone)]
pub enum Message {
    Text { to: String, content: String },
    Image { to: String, data: Vec<u8>, caption: Option<String> },
    // Additional message types as needed
}

#[derive(Debug, Clone)]
pub enum Event {
    MessageReceived(IncomingMessage),
    ConnectionStatusChanged(ConnectionStatus),
    QRCodeGenerated(String),
    LoginSuccess,
    LoginFailure(String),
}

#[derive(Debug, Clone)]
pub struct IncomingMessage {
    pub from: String,
    pub content: MessageContent,
    pub timestamp: SystemTime,
}
```

### Protocol Buffer Integration
The system will use generated Rust structs from WhatsApp's protobuf definitions:
```rust
// Generated from .proto files
pub mod wa_proto {
    include!(concat!(env!("OUT_DIR"), "/wa_proto.rs"));
}
```

## Error Handling

Comprehensive error handling using a custom error type:
```rust
#[derive(Debug, thiserror::Error)]
pub enum WhatsAppError {
    #[error("Connection error: {0}")]
    Connection(#[from] tokio_tungstenite::tungstenite::Error),
    
    #[error("Authentication failed: {0}")]
    Authentication(String),
    
    #[error("Protocol error: {0}")]
    Protocol(String),
    
    #[error("Encryption error: {0}")]
    Encryption(String),
    
    #[error("Serialization error: {0}")]
    Serialization(#[from] prost::DecodeError),
    
    #[error("IO error: {0}")]
    Io(#[from] std::io::Error),
}

pub type Result<T> = std::result::Result<T, WhatsAppError>;
```

## Testing Strategy

### Unit Tests
- Test each module independently with mock dependencies
- Test error handling paths and edge cases
- Test cryptographic functions with known test vectors
- Test Protocol Buffer serialization/deserialization

### Integration Tests
- Test full authentication flow with mock WhatsApp servers
- Test message sending/receiving end-to-end
- Test reconnection logic with simulated network failures
- Test session persistence and restoration

### Test Structure
```rust
#[cfg(test)]
mod tests {
    use super::*;
    use tokio_test;
    
    #[tokio::test]
    async fn test_connection_establishment() {
        // Test WebSocket connection
    }
    
    #[tokio::test]
    async fn test_qr_generation() {
        // Test QR code generation
    }
    
    #[tokio::test]
    async fn test_message_encryption() {
        // Test encryption/decryption
    }
}
```

## Dependencies

The implementation will use the following key crates:
- `tokio` - Async runtime
- `tokio-tungstenite` - WebSocket client
- `prost` - Protocol Buffers
- `serde` - Serialization
- `thiserror` - Error handling
- `qrcode` - QR code generation
- `snow` - Noise Protocol implementation
- `aes-gcm` - AES encryption
- `sha2` - SHA hashing
- `rand` - Random number generation
- `base64` - Base64 encoding/decoding
- `url` - URL parsing

## Security Considerations

1. **Key Management**: All cryptographic keys will be stored securely and never logged
2. **Session Storage**: Session data will be encrypted when stored to disk
3. **Memory Safety**: Use Rust's memory safety guarantees to prevent buffer overflows
4. **Constant-Time Operations**: Use constant-time cryptographic operations where applicable
5. **Input Validation**: Validate all incoming data before processing

## Performance Considerations

1. **Async Operations**: All I/O operations will be async to prevent blocking
2. **Connection Pooling**: Reuse connections where possible
3. **Message Batching**: Batch multiple messages when appropriate
4. **Memory Management**: Use efficient data structures and avoid unnecessary allocations
5. **Reconnection Strategy**: Implement exponential backoff for reconnection attempts