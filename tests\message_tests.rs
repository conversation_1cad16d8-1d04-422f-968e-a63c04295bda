use whatsmeow_rs::*;

/// Test message types
#[test]
fn test_message_types() {
    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Text message
    let text_msg = Message::Text {
        to: phone.clone(),
        content: "Hello, World!".to_string(),
    };

    match text_msg {
        Message::Text { to, content } => {
            assert_eq!(to.as_str(), "+1234567890");
            assert_eq!(content, "Hello, World!");
        }
        _ => panic!("Expected text message"),
    }

    // Image message
    let image_msg = Message::Image {
        to: phone.clone(),
        data: vec![0x89, 0x50, 0x4E, 0x47], // PNG header
        caption: Some("Test image".to_string()),
        mime_type: "image/png".to_string(),
    };

    match image_msg {
        Message::Image {
            to,
            data,
            caption,
            mime_type,
        } => {
            assert_eq!(to.as_str(), "+1234567890");
            assert_eq!(data, vec![0x89, 0x50, 0x4E, 0x47]);
            assert_eq!(caption, Some("Test image".to_string()));
            assert_eq!(mime_type, "image/png");
        }
        _ => panic!("Expected image message"),
    }
}

/// Test delivery status
#[test]
fn test_delivery_status() {
    let phone = PhoneNumber::new("+1234567890").unwrap();
    let status = MessageDeliveryStatus {
        message_id: "test_123".to_string(),
        recipient: phone.clone(),
        status: DeliveryStatus::Sent,
        timestamp: std::time::SystemTime::now(),
        error: None,
    };

    assert_eq!(status.message_id, "test_123");
    assert_eq!(status.recipient.as_str(), "+1234567890");
    assert_eq!(status.status, DeliveryStatus::Sent);
    assert!(status.error.is_none());
}
