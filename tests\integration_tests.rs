use whatsmeow_rs::*;
use std::sync::Arc;
use std::time::Duration;

/// Test basic client creation and configuration
#[tokio::test]
async fn test_client_creation() {
    // Test default client creation
    let client_result = WhatsAppClient::new().await;
    assert!(client_result.is_ok());

    // Test builder pattern
    let builder_result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(30))
        .reconnect_attempts(3)
        .build()
        .await;
    assert!(builder_result.is_ok());
}

/// Test phone number validation
#[test]
fn test_phone_number_validation() {
    // Valid phone numbers
    assert!(PhoneNumber::new("+1234567890").is_ok());
    assert!(PhoneNumber::new("+447700900123").is_ok());
    assert!(PhoneNumber::new("+12345678").is_ok()); // Minimum length

    // Invalid phone numbers
    assert!(PhoneNumber::new("1234567890").is_err()); // Missing +
    assert!(PhoneNumber::new("+123").is_err()); // Too short
    assert!(PhoneNumber::new("+1234567890123456").is_err()); // Too long
    assert!(PhoneNumber::new("+123abc7890").is_err()); // Contains letters
}

/// Test builder validation
#[tokio::test]
async fn test_builder_validation() {
    // Test invalid timeout
    let result = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(0))
        .build()
        .await;
    assert!(result.is_err());

    // Test invalid reconnect attempts
    let result = ClientBuilder::new().reconnect_attempts(0).build().await;
    assert!(result.is_err());

    // Test invalid server URL
    let result = ClientBuilder::new().server_url("invalid_url").build().await;
    assert!(result.is_err());
}