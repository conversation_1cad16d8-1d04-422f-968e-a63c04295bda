//! Test utilities for client module

#[cfg(test)]
pub mod test_helpers {
    use super::super::*;
    use crate::types::Event;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::sync::Mutex;

    /// Mock event handler for testing with enhanced functionality
    #[derive(Debug)]
    pub struct MockEventHandler {
        pub events: Arc<Mutex<Vec<Event>>>,
        #[allow(dead_code)]
        pub should_fail: Arc<Mutex<bool>>,
    }

    impl MockEventHandler {
        pub fn new() -> Self {
            Self {
                events: Arc::new(Mutex::new(Vec::new())),
                should_fail: Arc::new(Mutex::new(false)),
            }
        }

        /// Get all captured events
        #[allow(dead_code)]
        pub async fn get_events(&self) -> Vec<Event> {
            self.events.lock().await.clone()
        }

        /// Clear all captured events
        #[allow(dead_code)]
        pub async fn clear_events(&self) {
            self.events.lock().await.clear();
        }

        /// Wait for a specific number of events with timeout
        #[allow(dead_code)]
        pub async fn wait_for_event_count(&self, count: usize, timeout_duration: Duration) -> bool {
            let start = std::time::Instant::now();
            while start.elapsed() < timeout_duration {
                if self.events.lock().await.len() >= count {
                    return true;
                }
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
            false
        }

        /// Set whether the handler should fail on next event
        #[allow(dead_code)]
        pub async fn set_should_fail(&self, should_fail: bool) {
            *self.should_fail.lock().await = should_fail;
        }

        /// Get the count of events without cloning
        #[allow(dead_code)]
        pub async fn event_count(&self) -> usize {
            self.events.lock().await.len()
        }

        /// Wait for a specific event type
        #[allow(dead_code)]
        pub async fn wait_for_event_type<F>(&self, predicate: F, timeout_duration: Duration) -> bool
        where
            F: Fn(&Event) -> bool,
        {
            let start = std::time::Instant::now();
            while start.elapsed() < timeout_duration {
                let events = self.events.lock().await;
                if events.iter().any(&predicate) {
                    return true;
                }
                drop(events);
                tokio::time::sleep(Duration::from_millis(10)).await;
            }
            false
        }
    }

    #[async_trait::async_trait]
    impl EventHandler for MockEventHandler {
        async fn handle_event(&self, event: Event) -> Result<()> {
            self.events.lock().await.push(event);
            Ok(())
        }
    }

    /// Test client builder for flexible test setup
    pub struct TestClientBuilder {
        session_file: Option<String>,
        authenticated: bool,
        device_id: Option<String>,
        server_url: Option<String>,
    }

    impl TestClientBuilder {
        pub fn new() -> Self {
            Self {
                session_file: None,
                authenticated: false,
                device_id: None,
                server_url: None,
            }
        }

        #[allow(dead_code)]
        pub fn with_session_file(mut self, path: &str) -> Self {
            self.session_file = Some(path.to_string());
            self
        }

        #[allow(dead_code)]
        pub fn authenticated(mut self) -> Self {
            self.authenticated = true;
            self
        }

        #[allow(dead_code)]
        pub fn with_device_id(mut self, device_id: &str) -> Self {
            self.device_id = Some(device_id.to_string());
            self
        }

        #[allow(dead_code)]
        pub fn with_server_url(mut self, url: &str) -> Self {
            self.server_url = Some(url.to_string());
            self
        }

        pub async fn build(self) -> Result<WhatsAppClient> {
            let mut builder = ClientBuilder::new();

            if let Some(ref url) = self.server_url {
                builder = builder.server_url(url.clone());
            }

            if let Some(ref session_file) = self.session_file {
                builder = builder.session_file(session_file.clone());
            }

            let client = builder.build().await?;

            if self.authenticated {
                self.setup_authenticated_session(&client).await;
            }

            Ok(client)
        }

        async fn setup_authenticated_session(&self, client: &WhatsAppClient) {
            let mut session = client.session.lock().await;
            session.device_id = self
                .device_id
                .clone()
                .unwrap_or_else(|| "test_device_123".to_string());
            session.is_authenticated = true;
            session.client_token = "test_client_token".to_string();
            session.server_token = "test_server_token".to_string();
            session.encryption_keys = crate::client::session::EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
        }
    }

    /// Create a test client with default configuration
    pub async fn create_test_client() -> WhatsAppClient {
        TestClientBuilder::new()
            .build()
            .await
            .expect("Failed to create test client")
    }

    /// Create a test phone number
    #[allow(dead_code)]
    pub fn create_test_phone() -> crate::types::PhoneNumber {
        crate::types::PhoneNumber::new("+1234567890").unwrap()
    }

    /// Clean up test session file
    #[allow(dead_code)]
    pub fn cleanup_session_file(path: &str) {
        let _ = std::fs::remove_file(path);
    }

    /// Generate test image data
    #[allow(dead_code)]
    pub fn create_test_image_data(size_kb: usize) -> Vec<u8> {
        vec![0u8; size_kb * 1024]
    }

    /// Create test message content of specified length
    #[allow(dead_code)]
    pub fn create_test_message(length: usize) -> String {
        "a".repeat(length)
    }

    /// Test data generator for various scenarios
    pub struct TestDataGenerator;

    impl TestDataGenerator {
        /// Generate valid phone numbers for testing
        #[allow(dead_code)]
        pub fn valid_phone_numbers() -> Vec<&'static str> {
            vec![
                "+1234567890",
                "+12345678",
                "+123456789012345",
                "+447700900123",
                "+33123456789",
            ]
        }

        /// Generate invalid phone numbers for testing
        #[allow(dead_code)]
        pub fn invalid_phone_numbers() -> Vec<&'static str> {
            vec![
                "",
                "1234567890",        // No +
                "+123456",           // Too short
                "+1234567890123456", // Too long
                "+123abc7890",       // Non-digits
                "+",                 // Just +
            ]
        }

        /// Generate test MIME types
        #[allow(dead_code)]
        pub fn supported_mime_types() -> Vec<&'static str> {
            vec!["image/jpeg", "image/png", "image/gif", "image/webp"]
        }

        /// Generate unsupported MIME types
        #[allow(dead_code)]
        pub fn unsupported_mime_types() -> Vec<&'static str> {
            vec!["image/bmp", "image/tiff", "application/pdf", "text/plain"]
        }
    }
}
