{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and analyzes modified code for potential improvements including code smells, design patterns, and best practices. Generates suggestions for improving readability, maintainability, and performance while preserving functionality.", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.rs", "Cargo.toml", "**/*.rs"]}, "then": {"type": "askAgent", "prompt": "Analyze the recently modified code files for potential improvements. Focus on:\n\n1. **Code Smells**: Identify any code smells such as long methods, large classes, duplicate code, or complex conditionals\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n3. **Best Practices**: Check adherence to Rust best practices including error handling, memory safety, and idiomatic code\n4. **Readability**: Suggest improvements for variable naming, function organization, and code documentation\n5. **Maintainability**: Identify areas that could be refactored for easier maintenance and testing\n6. **Performance**: Suggest optimizations that could improve performance without sacrificing readability\n\nFor each suggestion, provide:\n- The specific issue or improvement opportunity\n- Why it matters for code quality\n- A concrete example of how to implement the improvement\n- The expected benefit (readability, performance, maintainability, etc.)\n\nEnsure all suggestions maintain the existing functionality and behavior of the code."}}