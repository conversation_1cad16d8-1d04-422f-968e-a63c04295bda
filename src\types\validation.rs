//! Message validation utilities

use crate::error::WhatsAppError;

/// Validation constants
pub struct ValidationLimits;

impl ValidationLimits {
    pub const MAX_TEXT_LENGTH: usize = 65536;
    pub const MAX_IMAGE_SIZE: usize = 16 * 1024 * 1024; // 16MB
    pub const MIN_PHONE_DIGITS: usize = 7;
    pub const MAX_PHONE_DIGITS: usize = 15;
}

/// Validate text message content
pub fn validate_text_content(content: &str) -> Result<(), WhatsAppError> {
    if content.is_empty() {
        return Err(WhatsAppError::InvalidInput(
            "Message content cannot be empty".to_string()
        ));
    }
    
    if content.len() > ValidationLimits::MAX_TEXT_LENGTH {
        return Err(WhatsAppError::InvalidInput(
            format!("Message content too long (max {} characters)", ValidationLimits::MAX_TEXT_LENGTH)
        ));
    }
    
    Ok(())
}

/// Validate image data and MIME type
pub fn validate_image_data(data: &[u8], mime_type: &str) -> Result<(), WhatsAppError> {
    if data.is_empty() {
        return Err(WhatsAppError::InvalidInput(
            "Image data cannot be empty".to_string()
        ));
    }
    
    if data.len() > ValidationLimits::MAX_IMAGE_SIZE {
        return Err(WhatsAppError::InvalidInput(
            format!("Image too large (max {}MB)", ValidationLimits::MAX_IMAGE_SIZE / (1024 * 1024))
        ));
    }
    
    if !mime_type.starts_with("image/") {
        return Err(WhatsAppError::InvalidInput(
            "Invalid MIME type for image".to_string()
        ));
    }
    
    Ok(())
}

/// Validate phone number format (E.164)
pub fn validate_phone_number(number: &str) -> Result<(), WhatsAppError> {
    if !number.starts_with('+') {
        return Err(WhatsAppError::InvalidInput(
            "Phone number must start with +".to_string()
        ));
    }
    
    let digits_only = &number[1..];
    
    if digits_only.len() < ValidationLimits::MIN_PHONE_DIGITS || digits_only.len() > ValidationLimits::MAX_PHONE_DIGITS {
        return Err(WhatsAppError::InvalidInput(
            format!("Phone number must have {}-{} digits after +", 
                ValidationLimits::MIN_PHONE_DIGITS, ValidationLimits::MAX_PHONE_DIGITS)
        ));
    }
    
    if !digits_only.chars().all(|c| c.is_ascii_digit()) {
        return Err(WhatsAppError::InvalidInput(
            "Phone number can only contain digits after +".to_string()
        ));
    }
    
    Ok(())
}