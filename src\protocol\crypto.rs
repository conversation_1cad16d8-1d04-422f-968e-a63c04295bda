//! Encryption and cryptographic operations

use crate::error::{Result, WhatsAppError};
use aes_gcm::{
    Aes256Gcm, Key, Nonce,
    aead::{Aead, OsRng, rand_core::RngCore},
};

use snow::{<PERSON><PERSON><PERSON>, HandshakeState, TransportState};
use std::collections::HashMap;

/// Manages all cryptographic operations
pub struct EncryptionManager {
    noise_state: Option<NoiseHandshakeState>,
    transport_state: Option<TransportState>,
    #[allow(dead_code)] // Will be used in future implementation
    signal_store: SignalStore,
    session_keys: HashMap<String, SessionKeys>,
}

/// Wrapper for Noise protocol handshake state
pub struct NoiseHandshakeState {
    handshake: HandshakeState,
}

/// Signal protocol store for managing encryption keys
pub struct SignalStore {
    identity_key: Option<[u8; 32]>,
    prekeys: HashMap<u32, [u8; 32]>,
    #[allow(dead_code)] // Will be used in future implementation
    signed_prekey: Option<[u8; 32]>,
}

/// Session keys for message encryption
#[derive(Clone)]
pub struct SessionKeys {
    pub encrypt_key: [u8; 32],
    pub decrypt_key: [u8; 32],
    pub mac_key: [u8; 32],
    pub counter: u64,
}

impl EncryptionManager {
    // Constants for cryptographic operations
    const HANDSHAKE_BUFFER_SIZE: usize = 65535;
    const NONCE_SIZE: usize = 12;
    const KEY_SIZE: usize = 32;
    const AUTH_TAG_SIZE: usize = 16;

    /// Create a new encryption manager
    pub fn new() -> Self {
        Self {
            noise_state: None,
            transport_state: None,
            signal_store: SignalStore::new(),
            session_keys: HashMap::new(),
        }
    }

    /// Initialize Noise protocol for initial handshake encryption
    pub fn initialize_noise_protocol(&mut self) -> Result<Vec<u8>> {
        // Initialize Noise protocol with Curve25519, ChaCha20Poly1305, SHA256
        let builder = Builder::new("Noise_XX_25519_ChaChaPoly_SHA256".parse().unwrap());
        let static_key = builder.generate_keypair().unwrap().private;
        let handshake = builder
            .local_private_key(&static_key)
            .build_initiator()
            .map_err(|e| {
                WhatsAppError::Encryption(format!("Failed to initialize Noise protocol: {}", e))
            })?;

        self.noise_state = Some(NoiseHandshakeState { handshake });

        // Generate initial handshake message
        let mut buf = vec![0u8; Self::HANDSHAKE_BUFFER_SIZE];
        let len = self
            .noise_state
            .as_mut()
            .unwrap()
            .handshake
            .write_message(&[], &mut buf)
            .map_err(|e| {
                WhatsAppError::Encryption(format!("Failed to write handshake message: {}", e))
            })?;

        buf.truncate(len);
        Ok(buf)
    }

    /// Process handshake response and complete Noise protocol setup
    ///
    /// Returns:
    /// - `Ok(None)` if handshake is complete and transport mode is established
    /// - `Ok(Some(Vec<u8>))` if another handshake message needs to be sent
    /// - `Err(WhatsAppError)` if an error occurs during processing
    pub fn process_handshake_response(&mut self, response: &[u8]) -> Result<Option<Vec<u8>>> {
        let noise_state = self.noise_state.as_mut().ok_or_else(|| {
            WhatsAppError::Encryption("Noise protocol not initialized".to_string())
        })?;

        let mut buf = vec![0u8; Self::HANDSHAKE_BUFFER_SIZE];
        let len = noise_state
            .handshake
            .read_message(response, &mut buf)
            .map_err(|e| {
                WhatsAppError::Encryption(format!("Failed to read handshake response: {}", e))
            })?;

        if noise_state.handshake.is_handshake_finished() {
            // Handshake complete, transition to transport mode
            // Take ownership of the noise state to transition to transport mode
            let noise_state = self.noise_state.take().unwrap();
            self.transport_state =
                Some(noise_state.handshake.into_transport_mode().map_err(|e| {
                    WhatsAppError::Encryption(format!(
                        "Failed to transition to transport mode: {}",
                        e
                    ))
                })?);
            Ok(None)
        } else {
            // Need to send another handshake message
            let mut response_buf = vec![0u8; Self::HANDSHAKE_BUFFER_SIZE];
            let response_len = noise_state
                .handshake
                .write_message(&buf[..len], &mut response_buf)
                .map_err(|e| {
                    WhatsAppError::Encryption(format!("Failed to write handshake message: {}", e))
                })?;

            response_buf.truncate(response_len);
            Ok(Some(response_buf))
        }
    }

    /// Generate session keys for a specific contact
    pub fn generate_session_keys(&mut self, contact_id: &str) -> Result<()> {
        let mut rng = OsRng;
        let mut encrypt_key = [0u8; 32];
        let mut decrypt_key = [0u8; 32];
        let mut mac_key = [0u8; 32];

        rng.fill_bytes(&mut encrypt_key);
        rng.fill_bytes(&mut decrypt_key);
        rng.fill_bytes(&mut mac_key);

        let session_keys = SessionKeys {
            encrypt_key,
            decrypt_key,
            mac_key,
            counter: 0,
        };

        self.session_keys
            .insert(contact_id.to_string(), session_keys);
        Ok(())
    }

    /// Encrypt a message using session keys
    pub fn encrypt_message(&mut self, contact_id: &str, plaintext: &[u8]) -> Result<Vec<u8>> {
        // First try Noise transport if available (for handshake messages)
        if let Some(transport) = &mut self.transport_state {
            let mut buf = vec![0u8; plaintext.len() + Self::AUTH_TAG_SIZE]; // Extra space for authentication tag
            let len = transport.write_message(plaintext, &mut buf).map_err(|e| {
                WhatsAppError::Encryption(format!("Failed to encrypt with Noise transport: {}", e))
            })?;
            buf.truncate(len);
            return Ok(buf);
        }

        // Use session keys for regular message encryption
        let session_keys = self.session_keys.get_mut(contact_id).ok_or_else(|| {
            WhatsAppError::Encryption(format!("No session keys found for contact: {}", contact_id))
        })?;

        // Use AES-GCM for message encryption
        let key = Key::<Aes256Gcm>::from_slice(&session_keys.encrypt_key);
        let cipher = <Aes256Gcm as aes_gcm::KeyInit>::new(key);

        // Generate nonce from counter
        let mut nonce_bytes = [0u8; Self::NONCE_SIZE];
        nonce_bytes[4..].copy_from_slice(&session_keys.counter.to_be_bytes());
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = cipher
            .encrypt(nonce, plaintext)
            .map_err(|e| WhatsAppError::Encryption(format!("Failed to encrypt message: {}", e)))?;

        // Increment counter
        session_keys.counter += 1;

        // Prepend nonce to ciphertext
        let mut result = nonce_bytes.to_vec();
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    /// Decrypt a message using session keys
    pub fn decrypt_message(&mut self, contact_id: &str, ciphertext: &[u8]) -> Result<Vec<u8>> {
        use aes_gcm::KeyInit;

        // First try Noise transport if available (for handshake messages)
        if let Some(transport) = &mut self.transport_state {
            let mut buf = vec![0u8; ciphertext.len()];
            let len = transport.read_message(ciphertext, &mut buf).map_err(|e| {
                WhatsAppError::Encryption(format!("Failed to decrypt with Noise transport: {}", e))
            })?;
            buf.truncate(len);
            return Ok(buf);
        }

        // Use session keys for regular message decryption
        let session_keys = self.session_keys.get(contact_id).ok_or_else(|| {
            WhatsAppError::Encryption(format!("No session keys found for contact: {}", contact_id))
        })?;

        if ciphertext.len() < Self::NONCE_SIZE {
            return Err(WhatsAppError::Encryption(
                "Ciphertext too short".to_string(),
            ));
        }

        // Extract nonce and ciphertext
        let nonce = Nonce::from_slice(&ciphertext[..Self::NONCE_SIZE]);
        let encrypted_data = &ciphertext[Self::NONCE_SIZE..];

        // Use AES-GCM for message decryption - use the same key as encryption
        let key = Key::<Aes256Gcm>::from_slice(&session_keys.encrypt_key);
        let cipher = Aes256Gcm::new(key);

        let plaintext = cipher
            .decrypt(nonce, encrypted_data)
            .map_err(|e| WhatsAppError::Encryption(format!("Failed to decrypt message: {}", e)))?;

        Ok(plaintext)
    }

    /// Derive key from password using PBKDF2
    pub fn derive_key_from_password(
        &self,
        password: &str,
        salt: &[u8],
        iterations: u32,
    ) -> [u8; 32] {
        let mut key = [0u8; Self::KEY_SIZE];
        let _ = pbkdf2::pbkdf2::<hmac::Hmac<sha2::Sha256>>(
            password.as_bytes(),
            salt,
            iterations,
            &mut key,
        );
        key
    }

    /// Generate HMAC for message authentication
    pub fn generate_hmac(&self, key: &[u8], data: &[u8]) -> Vec<u8> {
        use hmac::{Hmac, Mac};
        use sha2::Sha256;

        // Create alias for HMAC-SHA256
        type HmacSha256 = Hmac<Sha256>;

        let mut mac = HmacSha256::new_from_slice(key).expect("HMAC can take key of any size");
        mac.update(data);
        mac.finalize().into_bytes().to_vec()
    }

    /// Verify HMAC
    pub fn verify_hmac(&self, key: &[u8], data: &[u8], expected_hmac: &[u8]) -> bool {
        let computed_hmac = self.generate_hmac(key, data);
        computed_hmac.as_slice() == expected_hmac
    }

    /// Get or create session keys for a contact
    pub fn get_or_create_session_keys(&mut self, contact_id: &str) -> Result<&SessionKeys> {
        if !self.session_keys.contains_key(contact_id) {
            self.generate_session_keys(contact_id)?;
        }
        Ok(self.session_keys.get(contact_id).unwrap())
    }

    /// Check if Noise handshake is complete
    pub fn is_handshake_complete(&self) -> bool {
        self.transport_state.is_some()
    }
}

impl SignalStore {
    /// Create a new Signal store
    pub fn new() -> Self {
        Self {
            identity_key: None,
            prekeys: HashMap::new(),
            signed_prekey: None,
        }
    }

    /// Generate identity key
    pub fn generate_identity_key(&mut self) -> Result<[u8; 32]> {
        let mut rng = OsRng;
        let mut key = [0u8; 32];
        rng.fill_bytes(&mut key);
        self.identity_key = Some(key);
        Ok(key)
    }

    /// Generate prekeys
    pub fn generate_prekeys(&mut self, count: u32) -> Result<Vec<(u32, [u8; 32])>> {
        let mut rng = OsRng;
        let mut prekeys = Vec::new();

        for i in 0..count {
            let mut key = [0u8; 32];
            rng.fill_bytes(&mut key);
            self.prekeys.insert(i, key);
            prekeys.push((i, key));
        }

        Ok(prekeys)
    }

    /// Get identity key
    pub fn get_identity_key(&self) -> Option<[u8; 32]> {
        self.identity_key
    }

    /// Get prekey by ID
    pub fn get_prekey(&self, key_id: u32) -> Option<[u8; 32]> {
        self.prekeys.get(&key_id).copied()
    }
}

impl Default for EncryptionManager {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_encryption_manager_creation() {
        let manager = EncryptionManager::new();
        assert!(!manager.is_handshake_complete());
        assert!(manager.session_keys.is_empty());
    }

    #[test]
    fn test_noise_protocol_initialization() {
        let mut manager = EncryptionManager::new();
        let result = manager.initialize_noise_protocol();
        assert!(result.is_ok());
        assert!(manager.noise_state.is_some());

        let handshake_data = result.unwrap();
        assert!(!handshake_data.is_empty());
        // First handshake message should be 32 bytes (public key)
        assert_eq!(handshake_data.len(), 32);
    }

    #[test]
    fn test_session_key_generation() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        let result = manager.generate_session_keys(contact_id);
        assert!(result.is_ok());
        assert!(manager.session_keys.contains_key(contact_id));

        let keys = manager.session_keys.get(contact_id).unwrap();
        assert_eq!(keys.encrypt_key.len(), 32);
        assert_eq!(keys.decrypt_key.len(), 32);
        assert_eq!(keys.mac_key.len(), 32);
        assert_eq!(keys.counter, 0);
    }

    #[test]
    fn test_message_encryption_decryption() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";
        let plaintext = b"Hello, World!";

        // Generate session keys first
        manager.generate_session_keys(contact_id).unwrap();

        // Encrypt message
        let encrypted = manager.encrypt_message(contact_id, plaintext).unwrap();
        assert!(!encrypted.is_empty());
        assert_ne!(encrypted.as_slice(), plaintext);

        // Decrypt message
        let decrypted = manager.decrypt_message(contact_id, &encrypted).unwrap();
        assert_eq!(decrypted.as_slice(), plaintext);
    }

    #[test]
    fn test_encryption_with_different_messages() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        manager.generate_session_keys(contact_id).unwrap();

        let message1 = b"First message";
        let message2 = b"Second message";

        let encrypted1 = manager.encrypt_message(contact_id, message1).unwrap();
        let encrypted2 = manager.encrypt_message(contact_id, message2).unwrap();

        // Different messages should produce different ciphertexts
        assert_ne!(encrypted1, encrypted2);

        // Both should decrypt correctly
        let decrypted1 = manager.decrypt_message(contact_id, &encrypted1).unwrap();
        let decrypted2 = manager.decrypt_message(contact_id, &encrypted2).unwrap();

        assert_eq!(decrypted1.as_slice(), message1);
        assert_eq!(decrypted2.as_slice(), message2);
    }

    #[test]
    fn test_encryption_counter_increment() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";
        let plaintext = b"Test message";

        manager.generate_session_keys(contact_id).unwrap();

        // Get initial counter
        let initial_counter = manager.session_keys.get(contact_id).unwrap().counter;

        // Encrypt a message
        manager.encrypt_message(contact_id, plaintext).unwrap();

        // Counter should have incremented
        let new_counter = manager.session_keys.get(contact_id).unwrap().counter;
        assert_eq!(new_counter, initial_counter + 1);
    }

    #[test]
    fn test_encryption_without_session_keys() {
        let mut manager = EncryptionManager::new();
        let contact_id = "nonexistent_contact";
        let plaintext = b"Test message";

        // Should fail without session keys
        let result = manager.encrypt_message(contact_id, plaintext);
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::Encryption(msg) => {
                assert!(msg.contains("No session keys found"));
            }
            _ => panic!("Expected encryption error"),
        }
    }

    #[test]
    fn test_decryption_with_invalid_data() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        manager.generate_session_keys(contact_id).unwrap();

        // Try to decrypt invalid data (too short)
        let invalid_data = vec![1, 2, 3];
        let result = manager.decrypt_message(contact_id, &invalid_data);
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::Encryption(msg) => {
                assert!(msg.contains("Ciphertext too short"));
            }
            _ => panic!("Expected encryption error"),
        }
    }

    #[test]
    fn test_hmac_generation_and_verification() {
        let manager = EncryptionManager::new();
        let key = b"secret_key";
        let data = b"test_data";

        let hmac = manager.generate_hmac(key, data);
        assert_eq!(hmac.len(), 32); // SHA256 produces 32-byte hash

        // Verify HMAC
        assert!(manager.verify_hmac(key, data, &hmac));

        // Should fail with wrong key
        let wrong_key = b"wrong_key";
        assert!(!manager.verify_hmac(wrong_key, data, &hmac));

        // Should fail with wrong data
        let wrong_data = b"wrong_data";
        assert!(!manager.verify_hmac(key, wrong_data, &hmac));
    }

    #[test]
    fn test_key_derivation() {
        let manager = EncryptionManager::new();
        let password = "test_password";
        let salt = b"test_salt";
        let iterations = 1000;

        let key1 = manager.derive_key_from_password(password, salt, iterations);
        let key2 = manager.derive_key_from_password(password, salt, iterations);

        // Same inputs should produce same key
        assert_eq!(key1, key2);
        assert_eq!(key1.len(), 32);

        // Different password should produce different key
        let key3 = manager.derive_key_from_password("different_password", salt, iterations);
        assert_ne!(key1, key3);

        // Different salt should produce different key
        let key4 = manager.derive_key_from_password(password, b"different_salt", iterations);
        assert_ne!(key1, key4);
    }

    #[test]
    fn test_signal_store_identity_key_generation() {
        let mut store = SignalStore::new();
        assert!(store.get_identity_key().is_none());

        let key = store.generate_identity_key().unwrap();
        assert_eq!(key.len(), 32);

        let retrieved_key = store.get_identity_key().unwrap();
        assert_eq!(key, retrieved_key);
    }

    #[test]
    fn test_signal_store_prekey_generation() {
        let mut store = SignalStore::new();
        let count = 5;

        let prekeys = store.generate_prekeys(count).unwrap();
        assert_eq!(prekeys.len(), count as usize);

        // Check that all prekeys are stored and retrievable
        for (key_id, expected_key) in prekeys {
            let retrieved_key = store.get_prekey(key_id).unwrap();
            assert_eq!(retrieved_key, expected_key);
        }

        // Non-existent key should return None
        assert!(store.get_prekey(999).is_none());
    }

    #[test]
    fn test_get_or_create_session_keys() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        // Should create keys if they don't exist
        let keys1 = {
            let keys_ref = manager.get_or_create_session_keys(contact_id).unwrap();
            keys_ref.clone()
        };
        assert_eq!(keys1.counter, 0);

        // Should return existing keys on subsequent calls
        let keys2 = {
            let keys_ref = manager.get_or_create_session_keys(contact_id).unwrap();
            keys_ref.clone()
        };
        assert_eq!(keys1.encrypt_key, keys2.encrypt_key);
        assert_eq!(keys1.decrypt_key, keys2.decrypt_key);
        assert_eq!(keys1.mac_key, keys2.mac_key);
    }

    #[tokio::test]
    async fn test_noise_handshake_simulation() {
        // Simulate a basic Noise handshake between two parties
        let mut initiator = EncryptionManager::new();
        let _responder = EncryptionManager::new();

        // Initiator starts handshake
        let msg1 = initiator.initialize_noise_protocol().unwrap();
        assert!(!msg1.is_empty());

        // In a real scenario, responder would process this message
        // For now, we just verify the handshake was initialized
        assert!(initiator.noise_state.is_some());
        assert!(!initiator.is_handshake_complete());
    }

    #[test]
    fn test_encryption_with_known_vectors() {
        // Test with known AES-GCM test vectors
        let mut manager = EncryptionManager::new();
        let contact_id = "test_vector";

        // Create session with known keys for testing
        let session_keys = SessionKeys {
            encrypt_key: [0u8; 32], // All zeros for test
            decrypt_key: [0u8; 32], // All zeros for test
            mac_key: [0u8; 32],     // All zeros for test
            counter: 0,
        };
        manager
            .session_keys
            .insert(contact_id.to_string(), session_keys);

        let plaintext = b""; // Empty plaintext
        let encrypted = manager.encrypt_message(contact_id, plaintext).unwrap();

        // Should be able to decrypt back to original
        let decrypted = manager.decrypt_message(contact_id, &encrypted).unwrap();
        assert_eq!(decrypted.as_slice(), plaintext);
    }

    #[test]
    fn test_multiple_contacts_isolation() {
        let mut manager = EncryptionManager::new();
        let contact1 = "contact1";
        let contact2 = "contact2";
        let message = b"Secret message";

        // Generate keys for both contacts
        manager.generate_session_keys(contact1).unwrap();
        manager.generate_session_keys(contact2).unwrap();

        // Encrypt with contact1's keys
        let encrypted1 = manager.encrypt_message(contact1, message).unwrap();

        // Should not be able to decrypt with contact2's keys
        let result = manager.decrypt_message(contact2, &encrypted1);
        assert!(result.is_err());

        // But should work with correct keys
        let decrypted = manager.decrypt_message(contact1, &encrypted1).unwrap();
        assert_eq!(decrypted.as_slice(), message);
    }

    #[test]
    fn test_large_message_encryption() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        manager.generate_session_keys(contact_id).unwrap();

        // Test with a large message (1MB)
        let large_message = vec![0xAB; 1024 * 1024];

        let encrypted = manager.encrypt_message(contact_id, &large_message).unwrap();
        let decrypted = manager.decrypt_message(contact_id, &encrypted).unwrap();

        assert_eq!(decrypted, large_message);
    }

    #[test]
    fn test_error_handling_edge_cases() {
        let mut manager = EncryptionManager::new();
        let contact_id = "test_contact";

        // Test decryption without session keys
        let result = manager.decrypt_message(
            contact_id,
            &[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16],
        );
        assert!(result.is_err());

        // Test with corrupted ciphertext
        manager.generate_session_keys(contact_id).unwrap();
        let plaintext = b"test message";
        let mut encrypted = manager.encrypt_message(contact_id, plaintext).unwrap();

        // Corrupt the ciphertext
        if let Some(last) = encrypted.last_mut() {
            *last = last.wrapping_add(1);
        }

        let result = manager.decrypt_message(contact_id, &encrypted);
        assert!(result.is_err());
    }
}
