# Quick Start Guide

## Basic Usage

```rust
use whatsmeow_rs::{WhatsAppClient, PhoneNumber, Result};

#[tokio::main]
async fn main() -> Result<()> {
    // Create client with default configuration
    let mut client = WhatsAppClient::new().await?;
    
    // Connect and authenticate
    client.connect().await?;
    client.login().await?; // This will display a QR code to scan
    
    // Send a message
    let phone = PhoneNumber::new("+1234567890")?;
    let status = client.send_message(phone, "Hello from Rust!").await?;
    println!("Message sent with ID: {}", status.message_id);
    
    // Gracefully disconnect
    client.disconnect().await?;
    Ok(())
}
```

## Dependencies

Add these to your `Cargo.toml`:

```toml
[dependencies]
whatsmeow-rs = "0.1"
tokio = { version = "1.0", features = ["full"] }
```