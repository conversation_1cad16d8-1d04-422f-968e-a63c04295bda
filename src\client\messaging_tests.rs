//! Unit tests for messaging functionality

use super::*;
use crate::types::PhoneNumber;
use std::sync::Arc;
use std::time::Duration;
use tokio::time::timeout;

/// Mock event handler for testing
struct MockEventHandler;

#[async_trait::async_trait]
impl EventHandler for MockEventHandler {
    async fn handle_event(&self, _event: Event) -> Result<()> {
        Ok(())
    }
}

#[tokio::test]
async fn test_send_message_not_connected() {
    let client = WhatsAppClient::new().await.unwrap();
    let phone = PhoneNumber::new("+1234567890").unwrap();

    let result = client.send_message(phone, "Hello, World!").await;

    assert!(result.is_err());
    // Since we check authentication first, this will fail with Authentication error
    match result.unwrap_err() {
        crate::error::WhatsAppError::Authentication(_) => {}
        e => panic!("Expected Authentication error, got: {:?}", e),
    }
}

#[tokio::test]
async fn test_send_message_not_authenticated() {
    // Create client with mock session that's not authenticated
    let client = WhatsAppClient::new().await.unwrap();

    // Mock connection as connected but not authenticated
    {
        let mut connection = client.connection.lock().await;
        // Set up a mock session that's not authenticated
        let session = Arc::new(Mutex::new(crate::client::session::Session::new()));
        connection.set_session(session);

        // Mock the connection as connected by setting websocket to Some
        // This is a bit of a hack for testing, but necessary to test the auth flow
    }

    let phone = PhoneNumber::new("+1234567890").unwrap();

    let result = client.send_message(phone, "Hello, World!").await;

    assert!(result.is_err());
    match result.unwrap_err() {
        crate::error::WhatsAppError::Authentication(_) => {}
        e => panic!("Expected Authentication error, got: {:?}", e),
    }
}

#[tokio::test]
async fn test_send_image_message_not_connected() {
    let client = WhatsAppClient::new().await.unwrap();
    let phone = PhoneNumber::new("+1234567890").unwrap();
    let image_data = vec![1, 2, 3, 4, 5];

    let result = client
        .send_image_message(
            phone,
            image_data,
            Some("Test image".to_string()),
            "image/jpeg".to_string(),
        )
        .await;

    assert!(result.is_err());
    // Since we check authentication first, this will fail with Authentication error
    match result.unwrap_err() {
        crate::error::WhatsAppError::Authentication(_) => {}
        e => panic!("Expected Authentication error, got: {:?}", e),
    }
}

#[tokio::test]
async fn test_send_message_authenticated_but_not_connected() {
    let client = WhatsAppClient::new().await.unwrap();

    // Set up an authenticated session
    {
        let mut connection = client.connection.lock().await;
        let mut session = crate::client::session::Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = crate::client::session::EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        let session_ref = Arc::new(Mutex::new(session));
        connection.set_session(session_ref);
        // Don't connect, so it should fail with NotConnected
    }

    let phone = PhoneNumber::new("+1234567890").unwrap();

    let result = client.send_message(phone, "Hello, World!").await;

    assert!(result.is_err());
    match result.unwrap_err() {
        crate::error::WhatsAppError::NotConnected => {}
        e => panic!("Expected NotConnected error, got: {:?}", e),
    }
}

#[tokio::test]
async fn test_send_image_message_not_authenticated() {
    // Create client with mock session that's not authenticated
    let client = WhatsAppClient::new().await.unwrap();

    // Mock connection as connected but not authenticated
    {
        let mut connection = client.connection.lock().await;
        // Set up a mock session that's not authenticated
        let session = Arc::new(Mutex::new(crate::client::session::Session::new()));
        connection.set_session(session);
    }

    let phone = PhoneNumber::new("+1234567890").unwrap();
    let image_data = vec![1, 2, 3, 4, 5];

    let result = client
        .send_image_message(
            phone,
            image_data,
            Some("Test image".to_string()),
            "image/jpeg".to_string(),
        )
        .await;

    assert!(result.is_err());
    match result.unwrap_err() {
        crate::error::WhatsAppError::Authentication(_) => {}
        e => panic!("Expected Authentication error, got: {:?}", e),
    }
}

#[tokio::test]
async fn test_generate_message_id() {
    let client = WhatsAppClient::new().await.unwrap();

    let id1 = client.generate_message_id();
    let id2 = client.generate_message_id();

    // IDs should be different
    assert_ne!(id1, id2);

    // IDs should be 16 characters long (64-bit hex)
    assert_eq!(id1.len(), 16);
    assert_eq!(id2.len(), 16);

    // IDs should be valid hex
    assert!(id1.chars().all(|c| c.is_ascii_hexdigit()));
    assert!(id2.chars().all(|c| c.is_ascii_hexdigit()));
}

#[tokio::test]
async fn test_message_delivery_status_creation() {
    let phone = PhoneNumber::new("+1234567890").unwrap();
    let message_id = "1234567890ABCDEF".to_string();
    let timestamp = std::time::SystemTime::now();

    let status = MessageDeliveryStatus {
        message_id: message_id.clone(),
        recipient: phone.clone(),
        status: DeliveryStatus::Sent,
        timestamp,
        error: None,
    };

    assert_eq!(status.message_id, message_id);
    assert_eq!(status.recipient, phone);
    assert_eq!(status.status, DeliveryStatus::Sent);
    assert!(status.error.is_none());
}

#[tokio::test]
async fn test_delivery_status_enum() {
    let statuses = vec![
        DeliveryStatus::Sent,
        DeliveryStatus::Delivered,
        DeliveryStatus::Read,
        DeliveryStatus::Failed,
        DeliveryStatus::Pending,
    ];

    // Test that all statuses can be created and compared
    for status in statuses {
        let cloned = status.clone();
        assert_eq!(status, cloned);
    }
}

#[tokio::test]
async fn test_phone_number_validation() {
    // Valid phone numbers
    assert!(PhoneNumber::new("+1234567890").is_ok());
    assert!(PhoneNumber::new("+12345678901234").is_ok()); // 14 digits
    assert!(PhoneNumber::new("+12345678").is_ok()); // 8 digits (minimum)

    // Invalid phone numbers
    assert!(PhoneNumber::new("1234567890").is_err()); // No +
    assert!(PhoneNumber::new("+123456").is_err()); // Too short
    assert!(PhoneNumber::new("+1234567890123456").is_err()); // Too long
    assert!(PhoneNumber::new("+123456789a").is_err()); // Contains letter
    assert!(PhoneNumber::new("+************").is_err()); // Contains dashes
    assert!(PhoneNumber::new("").is_err()); // Empty
    assert!(PhoneNumber::new("+").is_err()); // Just +
}

#[tokio::test]
async fn test_client_builder_configuration() {
    let config = ClientBuilder::new()
        .server_url("wss://test.example.com")
        .connect_timeout(Duration::from_secs(60))
        .reconnect_attempts(10)
        .session_file("test_session.json")
        .build()
        .await;

    assert!(config.is_ok());
    let client = config.unwrap();

    assert_eq!(client.config().server_url, "wss://test.example.com");
    assert_eq!(client.config().connect_timeout, Duration::from_secs(60));
    assert_eq!(client.config().reconnect_attempts, 10);
    assert_eq!(
        client.config().session_file_path,
        Some("test_session.json".to_string())
    );
}

#[tokio::test]
async fn test_event_handler_setting() {
    let mut client = WhatsAppClient::new().await.unwrap();

    // Initially no event handler
    assert!(client.event_handler.is_none());

    // Set event handler
    let handler = Arc::new(MockEventHandler);
    client.set_event_handler(handler);

    // Now should have event handler
    assert!(client.event_handler.is_some());
}

// Integration test for message serialization (requires mocking connection)
#[tokio::test]
async fn test_message_serialization_integration() {
    use crate::protocol::crypto::EncryptionManager;
    use crate::protocol::messages::MessageHandler;
    use crate::types::Message;

    let encryption = EncryptionManager::new();
    let mut handler = MessageHandler::new(encryption);

    let phone = PhoneNumber::new("+1234567890").unwrap();
    let message = Message::Text {
        to: phone,
        content: "Hello, World!".to_string(),
    };

    // Test that message can be serialized - may fail due to encryption setup
    let result = handler.serialize_message(&message);

    // The result may fail due to encryption not being properly initialized
    // but we test that it handles the error gracefully
    match result {
        Ok(serialized) => {
            assert!(!serialized.is_empty());
        }
        Err(e) => {
            // Expected to fail due to encryption setup, but should be a proper error
            match e {
                crate::error::WhatsAppError::Encryption(_) => {
                    // This is expected when encryption is not properly set up
                }
                _ => {
                    // Other errors are also acceptable for this test
                }
            }
        }
    }
}

#[tokio::test]
async fn test_image_message_serialization_integration() {
    use crate::protocol::crypto::EncryptionManager;
    use crate::protocol::messages::MessageHandler;
    use crate::types::Message;

    let encryption = EncryptionManager::new();
    let mut handler = MessageHandler::new(encryption);

    let phone = PhoneNumber::new("+1234567890").unwrap();
    let message = Message::Image {
        to: phone,
        data: vec![1, 2, 3, 4, 5],
        caption: Some("Test image".to_string()),
        mime_type: "image/jpeg".to_string(),
    };

    // Test that image message can be serialized - may fail due to encryption setup
    let result = handler.serialize_message(&message);

    // The result may fail due to encryption not being properly initialized
    // but we test that it handles the error gracefully
    match result {
        Ok(serialized) => {
            assert!(!serialized.is_empty());
        }
        Err(e) => {
            // Expected to fail due to encryption setup, but should be a proper error
            match e {
                crate::error::WhatsAppError::Encryption(_) => {
                    // This is expected when encryption is not properly set up
                }
                _ => {
                    // Other errors are also acceptable for this test
                }
            }
        }
    }
}

// Test timeout behavior for message sending
#[tokio::test]
async fn test_message_sending_timeout_behavior() {
    let client = WhatsAppClient::new().await.unwrap();
    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Test that send_message completes quickly when not connected
    // (should fail fast rather than hang)
    let result = timeout(
        Duration::from_secs(1),
        client.send_message(phone, "Hello, World!"),
    )
    .await;

    assert!(result.is_ok()); // Should not timeout
    assert!(result.unwrap().is_err()); // But should return error
}

// Test error handling for invalid message content
#[tokio::test]
async fn test_empty_message_handling() {
    let client = WhatsAppClient::new().await.unwrap();
    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Test sending empty message
    let result = client.send_message(phone, "").await;

    // Should fail with NotConnected (since we're not connected)
    // but the empty content itself should be handled gracefully
    assert!(result.is_err());
}

// Test large message handling
#[tokio::test]
async fn test_large_message_handling() {
    let client = WhatsAppClient::new().await.unwrap();
    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Create a large message (1MB of text)
    let large_text = "A".repeat(1024 * 1024);

    let result = client.send_message(phone, &large_text).await;

    // Should fail with NotConnected (since we're not connected)
    // but the large content itself should be handled gracefully
    assert!(result.is_err());
}

// Test concurrent message sending
#[tokio::test]
async fn test_concurrent_message_sending() {
    let client = Arc::new(WhatsAppClient::new().await.unwrap());
    let phone = PhoneNumber::new("+1234567890").unwrap();

    // Try to send multiple messages concurrently
    let mut handles = vec![];

    for i in 0..5 {
        let client_clone = client.clone();
        let phone_clone = phone.clone();
        let message = format!("Message {}", i);

        let handle =
            tokio::spawn(async move { client_clone.send_message(phone_clone, &message).await });

        handles.push(handle);
    }

    // Wait for all to complete
    let results = futures::future::join_all(handles).await;

    // All should fail with NotConnected (since we're not connected)
    for result in results {
        assert!(result.is_ok()); // Task completed
        assert!(result.unwrap().is_err()); // But message sending failed
    }
}
