//! Connection lifecycle management

use crate::error::Result;
use crate::client::connection::Connection;
use crate::client::session::Session;
use std::sync::Arc;
use tokio::sync::Mutex;
use std::time::Duration;

#[derive(Debug, <PERSON><PERSON>, PartialEq)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Failed,
}

pub struct ConnectionManager {
    connection: Arc<Mutex<Connection>>,
    state: Arc<Mutex<ConnectionState>>,
    config: ConnectionConfig,
}

#[derive(Debug, Clone)]
pub struct ConnectionConfig {
    pub server_url: String,
    pub connect_timeout: Duration,
    pub reconnect_attempts: u32,
}

impl ConnectionManager {
    pub fn new(config: ConnectionConfig, session: Arc<Mutex<Session>>) -> Self {
        let mut connection = Connection::new(config.server_url.clone());
        connection.set_session(session);
        
        Self {
            connection: Arc::new(Mutex::new(connection)),
            state: Arc::new(Mutex::new(ConnectionState::Disconnected)),
            config,
        }
    }

    pub async fn connect(&self) -> Result<()> {
        self.update_state(ConnectionState::Connecting).await;
        
        let connect_future = async {
            let mut connection = self.connection.lock().await;
            connection.connect_with_session().await
        };

        match tokio::time::timeout(self.config.connect_timeout, connect_future).await {
            Ok(Ok(_)) => {
                self.update_state(ConnectionState::Connected).await;
                Ok(())
            }
            Ok(Err(e)) => {
                self.update_state(ConnectionState::Failed).await;
                Err(e)
            }
            Err(_) => {
                self.update_state(ConnectionState::Failed).await;
                Err(crate::error::WhatsAppError::Timeout {
                    timeout: self.config.connect_timeout,
                })
            }
        }
    }

    pub async fn disconnect(&self) -> Result<()> {
        let mut connection = self.connection.lock().await;
        connection.close().await?;
        self.update_state(ConnectionState::Disconnected).await;
        Ok(())
    }

    pub async fn is_connected(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.is_connected()
    }

    pub async fn state(&self) -> ConnectionState {
        let state = self.state.lock().await;
        state.clone()
    }

    async fn update_state(&self, new_state: ConnectionState) {
        let mut state = self.state.lock().await;
        *state = new_state;
    }

    pub fn connection(&self) -> Arc<Mutex<Connection>> {
        self.connection.clone()
    }
}