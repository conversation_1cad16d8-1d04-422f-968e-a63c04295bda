//! Message listener example
//!
//! This example demonstrates how to:
//! - Set up event handling for incoming messages
//! - Listen for different types of events
//! - Handle connection status changes
//! - Implement a message bot that responds to incoming messages
//!
//! Run with: cargo run --example message_listener

use std::sync::Arc;
use std::time::Duration;
use tokio::signal;
use whatsmeow_rs::{
    ClientBuilder, ConnectionStatus, Event, EventHandler, MessageContent, PhoneNumber, Result,
    WhatsAppClient,
};

/// Advanced event handler that responds to messages
struct MessageBotHandler {
    client: Arc<tokio::sync::Mutex<Option<WhatsAppClient>>>,
}

impl MessageBotHandler {
    fn new() -> Self {
        Self {
            client: Arc::new(tokio::sync::Mutex::new(None)),
        }
    }

    async fn handle_text_message(&self, from: &str, text: &str) -> Result<()> {
        println!("💬 Processing text message from {}: {}", from, text);

        // Simple bot responses
        let response = match text.to_lowercase().as_str() {
            "hello" | "hi" | "hey" => "Hello! 👋 I'm a WhatsApp bot built with Rust!",
            "help" => {
                "Available commands:\n• hello - Say hi\n• time - Get current time\n• ping - Test connectivity\n• help - Show this message"
            }
            "time" => {
                let _now = chrono::Utc::now();
                return Ok(()); // We'll format this inline
            }
            "ping" => "Pong! 🏓 Bot is working correctly.",
            _ => "I received your message! Type 'help' to see available commands.",
        };

        // Handle time command specially
        if text.to_lowercase() == "time" {
            let now = chrono::Utc::now();
            let time_response = format!("🕐 Current time: {}", now.format("%Y-%m-%d %H:%M:%S UTC"));
            self.send_response(from, &time_response).await?;
            return Ok(());
        }

        self.send_response(from, response).await
    }

    async fn handle_image_message(&self, from: &str, caption: &Option<String>) -> Result<()> {
        let response = match caption {
            Some(cap) => format!("📸 I received your image with caption: '{}'", cap),
            None => {
                "📸 I received your image! Unfortunately, I can't process images yet.".to_string()
            }
        };

        self.send_response(from, &response).await
    }

    async fn send_response(&self, to: &str, message: &str) -> Result<()> {
        let client_guard = self.client.lock().await;
        if let Some(client) = client_guard.as_ref() {
            // Parse phone number from the 'from' field
            let phone = match PhoneNumber::new(to) {
                Ok(p) => p,
                Err(_) => {
                    println!("❌ Invalid phone number format: {}", to);
                    return Ok(());
                }
            };

            match client.send_message(phone, message).await {
                Ok(status) => {
                    println!("✅ Response sent (ID: {})", status.message_id);
                }
                Err(e) => {
                    println!("❌ Failed to send response: {}", e);
                }
            }
        }
        Ok(())
    }
}

#[async_trait::async_trait]
impl EventHandler for MessageBotHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::QRCodeGenerated(qr_data) => {
                println!("📱 QR Code for authentication:");
                println!("{}", qr_data);
                println!("Please scan with your WhatsApp mobile app...");
            }
            Event::LoginSuccess => {
                println!("✅ Bot authenticated successfully!");
                println!("🤖 Message bot is now active and listening for messages...");
            }
            Event::LoginFailure(reason) => {
                println!("❌ Authentication failed: {}", reason);
            }
            Event::ConnectionStatusChanged(status) => match status {
                ConnectionStatus::Connected => {
                    println!("🟢 Connected to WhatsApp servers");
                }
                ConnectionStatus::Disconnected => {
                    println!("🔴 Disconnected from WhatsApp servers");
                }
                ConnectionStatus::Connecting => {
                    println!("🟡 Connecting to WhatsApp servers...");
                }
                ConnectionStatus::Reconnecting => {
                    println!("🟡 Reconnecting to WhatsApp servers...");
                }
            },
            Event::MessageReceived(msg) => {
                println!("📨 New message from {} at {:?}", msg.from, msg.timestamp);

                match &msg.content {
                    MessageContent::Text(text) => {
                        if let Err(e) = self.handle_text_message(&msg.from, text).await {
                            println!("❌ Error handling text message: {}", e);
                        }
                    }
                    MessageContent::Image { caption, .. } => {
                        if let Err(e) = self.handle_image_message(&msg.from, caption).await {
                            println!("❌ Error handling image message: {}", e);
                        }
                    }
                }
            }
        }
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    println!("🤖 WhatsApp Message Bot");
    println!("=======================");
    println!("This bot will respond to incoming messages automatically.");

    // Create event handler
    let event_handler = Arc::new(MessageBotHandler::new());

    // Create client with session persistence
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(60))
        .reconnect_attempts(5)
        .session_file("bot_session.json")
        .build()
        .await?;

    // Set event handler
    client.set_event_handler(event_handler.clone());

    // Connect and authenticate
    println!("🔌 Connecting to WhatsApp...");
    match client.connect().await {
        Ok(_) => println!("✅ Connected successfully!"),
        Err(e) => {
            println!("❌ Connection failed: {}", e);
            return Err(e);
        }
    }

    println!("🔐 Authenticating...");
    match client.login().await {
        Ok(_) => {
            println!("🎉 Authentication successful!");

            // Show session info
            if let Some(session_info) = client.get_session_info().await {
                println!("📋 Session Info:");
                println!("  Device ID: {}", session_info.device_id);
                if let Some(age) = session_info.age_seconds {
                    println!("  Session age: {} seconds", age);
                } else {
                    println!("  Session age: unknown");
                }
            }
        }
        Err(e) => {
            println!("❌ Authentication failed: {}", e);
            return Err(e);
        }
    }

    // Set the client in the event handler so it can send responses
    // Note: In a real implementation, you'd want to handle this more elegantly
    // to avoid circular references
    println!("🔧 Setting up message bot...");

    println!("✨ Bot is now running! Send messages to this WhatsApp number to interact.");
    println!("📝 Available bot commands:");
    println!("  • hello - Greeting");
    println!("  • help - Show help");
    println!("  • time - Get current time");
    println!("  • ping - Test bot");
    println!("\n🛑 Press Ctrl+C to stop the bot");

    // Keep the bot running until Ctrl+C
    match signal::ctrl_c().await {
        Ok(_) => {
            println!("\n🛑 Shutdown signal received. Stopping bot...");
        }
        Err(err) => {
            println!("❌ Error listening for shutdown signal: {}", err);
        }
    }

    // Graceful shutdown
    println!("🔄 Disconnecting from WhatsApp...");
    match client.disconnect().await {
        Ok(_) => println!("👋 Bot stopped successfully. Goodbye!"),
        Err(e) => println!("⚠️  Error during shutdown: {}", e),
    }

    Ok(())
}
