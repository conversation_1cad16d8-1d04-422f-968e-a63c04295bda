//! WebSocket connection management

use crate::client::session::Session;
use crate::error::{Result, WhatsAppError};
use futures_util::{SinkExt, StreamExt};
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::net::TcpStream;
use tokio::sync::Mutex;
use tokio::time::sleep;
use tokio_tungstenite::{MaybeTlsStream, WebSocketStream, connect_async, tungstenite::Message};
use tracing::{debug, error, info, warn};

/// WebSocket connection to WhatsApp servers
pub struct Connection {
    websocket: Option<WebSocketStream<MaybeTlsStream<TcpStream>>>,
    url: String,
    reconnect_attempts: u32,
    max_reconnect_attempts: u32,
    last_ping_time: Option<Instant>,
    ping_interval: Duration,
    connection_timeout: Duration,
    session: Option<Arc<Mutex<Session>>>,
}

/// Configuration builder for Connection
#[derive(Debug, Clone)]
pub struct ConnectionConfig {
    pub max_reconnect_attempts: u32,
    pub ping_interval: Duration,
    pub connection_timeout: Duration,
}

impl Default for ConnectionConfig {
    fn default() -> Self {
        Self {
            max_reconnect_attempts: 5,
            ping_interval: Duration::from_secs(30),
            connection_timeout: Duration::from_secs(30),
        }
    }
}

impl ConnectionConfig {
    pub fn new() -> Self {
        Self::default()
    }

    pub fn max_reconnect_attempts(mut self, attempts: u32) -> Self {
        self.max_reconnect_attempts = attempts;
        self
    }

    pub fn ping_interval(mut self, interval: Duration) -> Self {
        self.ping_interval = interval;
        self
    }

    pub fn connection_timeout(mut self, timeout: Duration) -> Self {
        self.connection_timeout = timeout;
        self
    }
}

impl Connection {
    /// Create a new connection with default configuration
    pub fn new(url: String) -> Self {
        Self::with_config(url, ConnectionConfig::default())
    }

    /// Create a new connection with custom configuration
    pub fn with_config(url: String, config: ConnectionConfig) -> Self {
        Self {
            websocket: None,
            url,
            reconnect_attempts: 0,
            max_reconnect_attempts: config.max_reconnect_attempts,
            last_ping_time: None,
            ping_interval: config.ping_interval,
            connection_timeout: config.connection_timeout,
            session: None,
        }
    }

    /// Establish WebSocket connection to WhatsApp servers
    pub async fn connect(&mut self) -> Result<()> {
        info!("Attempting to connect to WebSocket: {}", self.url);

        // Close existing connection if any
        if self.websocket.is_some() {
            debug!("Closing existing connection before reconnecting");
            self.close().await?;
        }

        // Parse and validate URL
        let url = url::Url::parse(&self.url).map_err(|e| {
            error!("Invalid WebSocket URL: {}", e);
            WhatsAppError::InvalidConfig(format!("Invalid WebSocket URL: {}", e))
        })?;

        // Establish WebSocket connection with timeout
        let connect_future = connect_async(url.as_str());

        let (ws_stream, response) = tokio::time::timeout(self.connection_timeout, connect_future)
            .await
            .map_err(|_| {
                error!("Connection timeout after {:?}", self.connection_timeout);
                WhatsAppError::Timeout {
                    timeout: self.connection_timeout,
                }
            })?
            .map_err(|e| {
                error!("WebSocket connection failed: {}", e);
                WhatsAppError::Connection(e)
            })?;

        // Validate response status
        if response.status() != 101 {
            error!("Unexpected HTTP response status: {}", response.status());
            return Err(WhatsAppError::Connection(
                tokio_tungstenite::tungstenite::Error::Http(response),
            ));
        }

        self.websocket = Some(ws_stream);
        self.reconnect_attempts = 0;
        info!("Successfully connected to WebSocket");

        Ok(())
    }

    /// Send a frame over the WebSocket
    pub async fn send_frame(&mut self, data: Vec<u8>) -> Result<()> {
        let websocket = self.websocket.as_mut().ok_or(WhatsAppError::NotConnected)?;

        let message = Message::Binary(data.into());
        websocket
            .send(message)
            .await
            .map_err(WhatsAppError::Connection)?;

        Ok(())
    }

    /// Receive a frame from the WebSocket
    pub async fn receive_frame(&mut self) -> Result<Vec<u8>> {
        let websocket = self.websocket.as_mut().ok_or(WhatsAppError::NotConnected)?;

        loop {
            match websocket.next().await {
                Some(Ok(Message::Binary(data))) => return Ok(data.to_vec()),
                Some(Ok(Message::Text(text))) => return Ok(bytes::Bytes::from(text).to_vec()),
                Some(Ok(Message::Close(_))) => {
                    self.websocket = None;
                    return Err(WhatsAppError::Connection(
                        tokio_tungstenite::tungstenite::Error::ConnectionClosed,
                    ));
                }
                Some(Ok(Message::Ping(data))) => {
                    // Respond to ping with pong and continue loop
                    let pong = Message::Pong(data);
                    websocket
                        .send(pong)
                        .await
                        .map_err(WhatsAppError::Connection)?;
                    // Continue loop to get the next actual message
                }
                Some(Ok(Message::Pong(_))) => {
                    // Ignore pong messages and continue loop
                }
                Some(Ok(Message::Frame(_))) => {
                    // Raw frames are not expected in normal operation
                    return Err(WhatsAppError::Protocol(
                        "Unexpected raw frame received".to_string(),
                    ));
                }
                Some(Err(e)) => return Err(WhatsAppError::Connection(e)),
                None => {
                    self.websocket = None;
                    return Err(WhatsAppError::Connection(
                        tokio_tungstenite::tungstenite::Error::ConnectionClosed,
                    ));
                }
            }
        }
    }

    /// Close the connection gracefully
    pub async fn close(&mut self) -> Result<()> {
        if let Some(mut websocket) = self.websocket.take() {
            // Send close frame
            let close_frame = Message::Close(None);
            if let Err(e) = websocket.send(close_frame).await {
                // Log the error but don't fail the close operation
                warn!("Failed to send close frame: {}", e);
            }

            // Close the underlying connection
            if let Err(e) = websocket.close(None).await {
                // Log the error but don't fail the close operation
                warn!("Failed to close WebSocket: {}", e);
            }
        }

        self.websocket = None;
        Ok(())
    }

    /// Check if the connection is currently active
    pub fn is_connected(&self) -> bool {
        self.websocket.is_some()
    }

    /// Get the current reconnection attempt count
    pub fn reconnect_attempts(&self) -> u32 {
        self.reconnect_attempts
    }

    /// Increment reconnection attempt counter
    pub(crate) fn increment_reconnect_attempts(&mut self) {
        self.reconnect_attempts += 1;
    }

    /// Reset reconnection attempt counter
    pub(crate) fn reset_reconnect_attempts(&mut self) {
        self.reconnect_attempts = 0;
    }

    /// Attempt to reconnect with exponential backoff strategy
    pub async fn reconnect(&mut self) -> Result<()> {
        while self.reconnect_attempts < self.max_reconnect_attempts {
            // Calculate exponential backoff delay
            let base_delay = Duration::from_secs(1);
            let max_delay = Duration::from_secs(60);
            let delay = std::cmp::min(base_delay * 2_u32.pow(self.reconnect_attempts), max_delay);

            // Wait before attempting reconnection
            sleep(delay).await;

            self.increment_reconnect_attempts();

            // Attempt to reconnect
            match self.connect().await {
                Ok(()) => {
                    self.reset_reconnect_attempts();
                    return Ok(());
                }
                Err(_) => {
                    // Continue loop to try again if we haven't exceeded max attempts
                    continue;
                }
            }
        }

        // All reconnection attempts failed
        Err(WhatsAppError::Connection(
            tokio_tungstenite::tungstenite::Error::ConnectionClosed,
        ))
    }

    /// Send a ping frame to check connection health
    pub async fn send_ping(&mut self) -> Result<()> {
        let websocket = self.websocket.as_mut().ok_or(WhatsAppError::NotConnected)?;

        let ping_data = b"ping".to_vec();
        let ping_message = Message::Ping(ping_data.into());

        websocket
            .send(ping_message)
            .await
            .map_err(WhatsAppError::Connection)?;

        self.last_ping_time = Some(Instant::now());
        Ok(())
    }

    /// Check if a ping should be sent based on the ping interval
    pub fn should_send_ping(&self) -> bool {
        match self.last_ping_time {
            Some(last_ping) => last_ping.elapsed() >= self.ping_interval,
            None => true, // Send initial ping
        }
    }

    /// Maintain connection health by sending periodic pings
    pub async fn maintain_connection(&mut self) -> Result<()> {
        if !self.is_connected() {
            return Err(WhatsAppError::NotConnected);
        }

        if self.should_send_ping() {
            self.send_ping().await?;
        }

        Ok(())
    }

    /// Connect with automatic reconnection on failure
    pub async fn connect_with_retry(&mut self) -> Result<()> {
        match self.connect().await {
            Ok(()) => Ok(()),
            Err(_) => {
                // If initial connection fails, try reconnecting
                self.reconnect().await
            }
        }
    }

    /// Receive frame with automatic reconnection on connection loss
    pub async fn receive_frame_with_retry(&mut self) -> Result<Vec<u8>> {
        match self.receive_frame().await {
            Ok(data) => Ok(data),
            Err(WhatsAppError::Connection(_)) => {
                // Connection lost, try to reconnect
                self.reconnect().await?;
                // Try receiving again after reconnection
                self.receive_frame().await
            }
            Err(e) => Err(e),
        }
    }

    /// Send frame with automatic reconnection on connection loss
    pub async fn send_frame_with_retry(&mut self, data: &[u8]) -> Result<()> {
        match self.send_frame(data.to_vec()).await {
            Ok(()) => Ok(()),
            Err(WhatsAppError::Connection(_)) => {
                // Connection lost, try to reconnect
                self.reconnect().await?;
                // Try sending again after reconnection
                self.send_frame(data.to_vec()).await
            }
            Err(e) => Err(e),
        }
    }

    /// Get maximum reconnection attempts
    pub fn max_reconnect_attempts(&self) -> u32 {
        self.max_reconnect_attempts
    }

    /// Set maximum reconnection attempts
    pub fn set_max_reconnect_attempts(&mut self, max_attempts: u32) {
        self.max_reconnect_attempts = max_attempts;
    }

    /// Get ping interval
    pub fn ping_interval(&self) -> Duration {
        self.ping_interval
    }

    /// Set ping interval
    pub fn set_ping_interval(&mut self, interval: Duration) {
        self.ping_interval = interval;
    }

    /// Get connection timeout
    pub fn connection_timeout(&self) -> Duration {
        self.connection_timeout
    }

    /// Set connection timeout
    pub fn set_connection_timeout(&mut self, timeout: Duration) {
        self.connection_timeout = timeout;
    }

    /// Set session for authentication and state management
    pub fn set_session(&mut self, session: Arc<Mutex<Session>>) {
        self.session = Some(session);
    }

    /// Get session reference
    pub fn session(&self) -> Option<Arc<Mutex<Session>>> {
        self.session.clone()
    }

    /// Check if session is available and valid
    pub async fn has_valid_session(&self) -> bool {
        if let Some(session_ref) = &self.session {
            let session = session_ref.lock().await;
            session.is_valid()
        } else {
            false
        }
    }

    /// Check if session needs refresh
    pub async fn session_needs_refresh(&self) -> bool {
        if let Some(session_ref) = &self.session {
            let session = session_ref.lock().await;
            session.needs_refresh()
        } else {
            true // No session means we need to authenticate
        }
    }

    /// Update session last seen timestamp
    pub async fn update_session_last_seen(&self) -> Result<()> {
        if let Some(session_ref) = &self.session {
            let mut session = session_ref.lock().await;
            session.update_last_seen();
            debug!("Updated session last seen timestamp");
            Ok(())
        } else {
            Err(WhatsAppError::Session("No session available".to_string()))
        }
    }

    /// Connect with session-aware authentication
    pub async fn connect_with_session(&mut self) -> Result<()> {
        // First establish the WebSocket connection
        self.connect().await?;

        // Update session last seen if we have a session
        if self.session.is_some() {
            self.update_session_last_seen().await?;
        }

        // If we have a valid session, we can proceed with authenticated connection
        if self.has_valid_session().await {
            info!("Connected with valid session");
            Ok(())
        } else {
            warn!("Connected but session is invalid or missing");
            // Connection is established but authentication will be needed
            Ok(())
        }
    }

    /// Reconnect with session restoration
    pub async fn reconnect_with_session(&mut self) -> Result<()> {
        while self.reconnect_attempts < self.max_reconnect_attempts {
            // Calculate exponential backoff delay
            let base_delay = Duration::from_secs(1);
            let max_delay = Duration::from_secs(60);
            let delay = std::cmp::min(base_delay * 2_u32.pow(self.reconnect_attempts), max_delay);

            // Wait before attempting reconnection
            sleep(delay).await;

            self.increment_reconnect_attempts();

            // Attempt to reconnect with session
            match self.connect_with_session().await {
                Ok(()) => {
                    self.reset_reconnect_attempts();

                    // Check if session is still valid after reconnection
                    if !self.has_valid_session().await {
                        warn!("Reconnected but session is no longer valid");
                        return Err(WhatsAppError::Session(
                            "Session expired during reconnection".to_string(),
                        ));
                    }

                    info!("Successfully reconnected with valid session");
                    return Ok(());
                }
                Err(e) => {
                    warn!(
                        "Reconnection attempt {} failed: {}",
                        self.reconnect_attempts, e
                    );
                    // Continue loop to try again if we haven't exceeded max attempts
                    continue;
                }
            }
        }

        // All reconnection attempts failed
        error!(
            "All reconnection attempts failed after {} tries",
            self.max_reconnect_attempts
        );
        Err(WhatsAppError::Connection(
            tokio_tungstenite::tungstenite::Error::ConnectionClosed,
        ))
    }

    /// Send authenticated frame (updates session last seen)
    pub async fn send_authenticated_frame(&mut self, data: Vec<u8>) -> Result<()> {
        // Check if we have a valid session
        if !self.has_valid_session().await {
            return Err(WhatsAppError::Session(
                "Invalid or missing session".to_string(),
            ));
        }

        // Send the frame
        self.send_frame(data).await?;

        // Update session last seen
        self.update_session_last_seen().await?;

        Ok(())
    }

    /// Receive frame with session validation
    pub async fn receive_authenticated_frame(&mut self) -> Result<Vec<u8>> {
        // Check if we have a valid session
        if !self.has_valid_session().await {
            return Err(WhatsAppError::Session(
                "Invalid or missing session".to_string(),
            ));
        }

        // Receive the frame
        let data = self.receive_frame().await?;

        // Update session last seen
        self.update_session_last_seen().await?;

        Ok(data)
    }

    /// Send authenticated frame with retry and session management
    pub async fn send_authenticated_frame_with_retry(&mut self, data: &[u8]) -> Result<()> {
        // Check session validity first
        if self.session_needs_refresh().await {
            return Err(WhatsAppError::Session("Session needs refresh".to_string()));
        }

        match self.send_authenticated_frame(data.to_vec()).await {
            Ok(()) => Ok(()),
            Err(WhatsAppError::Connection(_)) => {
                // Connection lost, try to reconnect with session
                self.reconnect_with_session().await?;
                // Try sending again after reconnection
                self.send_authenticated_frame(data.to_vec()).await
            }
            Err(WhatsAppError::Session(_)) => {
                // Session issue, try to reconnect which will validate session
                self.reconnect_with_session().await?;
                // Try sending again after reconnection
                self.send_authenticated_frame(data.to_vec()).await
            }
            Err(e) => Err(e),
        }
    }

    /// Receive authenticated frame with retry and session management
    pub async fn receive_authenticated_frame_with_retry(&mut self) -> Result<Vec<u8>> {
        // Check session validity first
        if self.session_needs_refresh().await {
            return Err(WhatsAppError::Session("Session needs refresh".to_string()));
        }

        match self.receive_authenticated_frame().await {
            Ok(data) => Ok(data),
            Err(WhatsAppError::Connection(_)) => {
                // Connection lost, try to reconnect with session
                self.reconnect_with_session().await?;
                // Try receiving again after reconnection
                self.receive_authenticated_frame().await
            }
            Err(WhatsAppError::Session(_)) => {
                // Session issue, try to reconnect which will validate session
                self.reconnect_with_session().await?;
                // Try receiving again after reconnection
                self.receive_authenticated_frame().await
            }
            Err(e) => Err(e),
        }
    }

    /// Check if re-authentication is needed
    pub async fn needs_reauthentication(&self) -> bool {
        if let Some(session_ref) = &self.session {
            let session = session_ref.lock().await;
            !session.is_valid() || session.needs_refresh()
        } else {
            true // No session means we need authentication
        }
    }

    /// Clear session (for logout)
    pub async fn clear_session(&mut self) -> Result<()> {
        if let Some(session_ref) = &self.session {
            let mut session = session_ref.lock().await;
            session.clear_auth();
            info!("Session cleared");
        }
        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_connection_creation() {
        let url = "wss://web.whatsapp.com/ws/chat".to_string();
        let connection = Connection::new(url.clone());

        assert_eq!(connection.url, url);
        assert!(!connection.is_connected());
        assert_eq!(connection.reconnect_attempts(), 0);
    }

    #[tokio::test]
    async fn test_connection_invalid_url() {
        let mut connection = Connection::new("invalid-url".to_string());

        let result = connection.connect().await;
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::InvalidConfig(msg) => {
                assert!(msg.contains("Invalid WebSocket URL"));
            }
            _ => panic!("Expected InvalidConfig error"),
        }
    }

    #[tokio::test]
    async fn test_send_frame_not_connected() {
        let mut connection = Connection::new("wss://example.com".to_string());

        let result = connection.send_frame(vec![1, 2, 3]).await;
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::NotConnected => {}
            _ => panic!("Expected NotConnected error"),
        }
    }

    #[tokio::test]
    async fn test_receive_frame_not_connected() {
        let mut connection = Connection::new("wss://example.com".to_string());

        let result = connection.receive_frame().await;
        assert!(result.is_err());

        match result.unwrap_err() {
            WhatsAppError::NotConnected => {}
            _ => panic!("Expected NotConnected error"),
        }
    }

    #[tokio::test]
    async fn test_close_not_connected() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Should not error when closing a non-connected connection
        let result = connection.close().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_reconnect_attempts_tracking() {
        let mut connection = Connection::new("wss://example.com".to_string());

        assert_eq!(connection.reconnect_attempts(), 0);

        connection.increment_reconnect_attempts();
        assert_eq!(connection.reconnect_attempts(), 1);

        connection.increment_reconnect_attempts();
        assert_eq!(connection.reconnect_attempts(), 2);

        connection.reset_reconnect_attempts();
        assert_eq!(connection.reconnect_attempts(), 0);
    }

    #[tokio::test]
    async fn test_connection_with_config() {
        let url = "wss://example.com".to_string();
        let max_attempts = 10;
        let ping_interval = Duration::from_secs(60);
        let connection_timeout = Duration::from_secs(45);

        let config = ConnectionConfig::new()
            .max_reconnect_attempts(max_attempts)
            .ping_interval(ping_interval)
            .connection_timeout(connection_timeout);

        let connection = Connection::with_config(url.clone(), config);

        assert_eq!(connection.url, url);
        assert_eq!(connection.max_reconnect_attempts(), max_attempts);
        assert_eq!(connection.ping_interval(), ping_interval);
        assert_eq!(connection.connection_timeout(), connection_timeout);
        assert!(!connection.is_connected());
        assert_eq!(connection.reconnect_attempts(), 0);
    }

    #[tokio::test]
    async fn test_ping_functionality() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Should send ping initially
        assert!(connection.should_send_ping());

        // Test ping when not connected
        let result = connection.send_ping().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::NotConnected => {}
            _ => panic!("Expected NotConnected error"),
        }
    }

    #[tokio::test]
    async fn test_maintain_connection_not_connected() {
        let mut connection = Connection::new("wss://example.com".to_string());

        let result = connection.maintain_connection().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::NotConnected => {}
            _ => panic!("Expected NotConnected error"),
        }
    }

    #[tokio::test]
    async fn test_reconnect_max_attempts_exceeded() {
        let config = ConnectionConfig::new()
            .max_reconnect_attempts(2)
            .ping_interval(Duration::from_secs(30))
            .connection_timeout(Duration::from_secs(30));

        let mut connection = Connection::with_config(
            "wss://invalid-server-that-does-not-exist.com".to_string(),
            config,
        );

        // This should fail after 2 attempts
        let result = connection.reconnect().await;
        assert!(result.is_err());
        assert_eq!(connection.reconnect_attempts(), 2);
    }

    #[tokio::test]
    async fn test_connect_with_retry_invalid_url() {
        let config = ConnectionConfig::new()
            .max_reconnect_attempts(1)
            .ping_interval(Duration::from_secs(30))
            .connection_timeout(Duration::from_secs(30));

        let mut connection = Connection::with_config("invalid-url".to_string(), config);

        let result = connection.connect_with_retry().await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_send_frame_with_retry_not_connected() {
        let config = ConnectionConfig::new()
            .max_reconnect_attempts(1)
            .ping_interval(Duration::from_secs(30))
            .connection_timeout(Duration::from_secs(30));

        let mut connection =
            Connection::with_config("wss://invalid-server.com".to_string(), config);

        let result = connection.send_frame_with_retry(&[1, 2, 3]).await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_receive_frame_with_retry_not_connected() {
        let config = ConnectionConfig::new()
            .max_reconnect_attempts(1)
            .ping_interval(Duration::from_secs(30))
            .connection_timeout(Duration::from_secs(30));

        let mut connection =
            Connection::with_config("wss://invalid-server.com".to_string(), config);

        let result = connection.receive_frame_with_retry().await;
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_configuration_setters() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Test setting max reconnect attempts
        connection.set_max_reconnect_attempts(10);
        assert_eq!(connection.max_reconnect_attempts(), 10);

        // Test setting ping interval
        let new_interval = Duration::from_secs(60);
        connection.set_ping_interval(new_interval);
        assert_eq!(connection.ping_interval(), new_interval);

        // Test setting connection timeout
        let new_timeout = Duration::from_secs(45);
        connection.set_connection_timeout(new_timeout);
        assert_eq!(connection.connection_timeout(), new_timeout);
    }

    #[tokio::test]
    async fn test_ping_timing() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Initially should send ping
        assert!(connection.should_send_ping());

        // Simulate setting last ping time to now
        connection.last_ping_time = Some(Instant::now());

        // Should not send ping immediately after setting time
        assert!(!connection.should_send_ping());

        // Set ping interval to very short duration for testing
        connection.set_ping_interval(Duration::from_millis(1));

        // Wait a bit and check again
        tokio::time::sleep(Duration::from_millis(2)).await;
        assert!(connection.should_send_ping());
    }

    // Session-related tests
    #[tokio::test]
    async fn test_session_management() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Initially no session
        assert!(connection.session().is_none());
        assert!(!connection.has_valid_session().await);
        assert!(connection.session_needs_refresh().await);
        assert!(connection.needs_reauthentication().await);

        // Create and set a session
        let session = Arc::new(Mutex::new(Session::new()));
        connection.set_session(session.clone());

        assert!(connection.session().is_some());
        // Session should be invalid initially (not authenticated)
        assert!(!connection.has_valid_session().await);
        assert!(connection.needs_reauthentication().await);
    }

    #[tokio::test]
    async fn test_session_with_authentication() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Create authenticated session
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = crate::client::session::EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        let session_ref = Arc::new(Mutex::new(session));
        connection.set_session(session_ref);

        // Now session should be valid
        assert!(connection.has_valid_session().await);
        assert!(!connection.session_needs_refresh().await);
        assert!(!connection.needs_reauthentication().await);
    }

    #[tokio::test]
    async fn test_session_update_last_seen() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Test without session
        let result = connection.update_session_last_seen().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("No session available"));
            }
            _ => panic!("Expected Session error"),
        }

        // Test with session
        let session = Arc::new(Mutex::new(Session::new()));
        connection.set_session(session.clone());

        let original_time = {
            let session_guard = session.lock().await;
            session_guard.last_seen
        };

        // Wait a bit to ensure time difference
        tokio::time::sleep(Duration::from_millis(10)).await;

        let result = connection.update_session_last_seen().await;
        assert!(result.is_ok());

        let updated_time = {
            let session_guard = session.lock().await;
            session_guard.last_seen
        };

        assert!(updated_time > original_time);
    }

    #[tokio::test]
    async fn test_clear_session() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Create authenticated session
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let session_ref = Arc::new(Mutex::new(session));
        connection.set_session(session_ref.clone());

        // Verify session is authenticated
        {
            let session_guard = session_ref.lock().await;
            assert!(session_guard.is_authenticated);
            assert!(!session_guard.client_token.is_empty());
            assert!(!session_guard.server_token.is_empty());
        }

        // Clear session
        let result = connection.clear_session().await;
        assert!(result.is_ok());

        // Verify session is cleared
        {
            let session_guard = session_ref.lock().await;
            assert!(!session_guard.is_authenticated);
            assert!(session_guard.client_token.is_empty());
            assert!(session_guard.server_token.is_empty());
        }
    }

    #[tokio::test]
    async fn test_authenticated_frame_operations_without_session() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Test send authenticated frame without session
        let result = connection.send_authenticated_frame(vec![1, 2, 3]).await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Invalid or missing session"));
            }
            _ => panic!("Expected Session error"),
        }

        // Test receive authenticated frame without session
        let result = connection.receive_authenticated_frame().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Invalid or missing session"));
            }
            _ => panic!("Expected Session error"),
        }
    }

    #[tokio::test]
    async fn test_authenticated_frame_operations_with_invalid_session() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Create invalid session (not authenticated)
        let session = Arc::new(Mutex::new(Session::new()));
        connection.set_session(session);

        // Test send authenticated frame with invalid session
        let result = connection.send_authenticated_frame(vec![1, 2, 3]).await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Invalid or missing session"));
            }
            _ => panic!("Expected Session error"),
        }

        // Test receive authenticated frame with invalid session
        let result = connection.receive_authenticated_frame().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Invalid or missing session"));
            }
            _ => panic!("Expected Session error"),
        }
    }

    #[tokio::test]
    async fn test_session_needs_refresh_with_old_session() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Create session with old last_seen time
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = crate::client::session::EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Set last_seen to 8 days ago (should need refresh)
        session.last_seen = chrono::Utc::now() - chrono::Duration::days(8);

        let session_ref = Arc::new(Mutex::new(session));
        connection.set_session(session_ref);

        // Session should be valid but need refresh
        assert!(connection.has_valid_session().await);
        assert!(connection.session_needs_refresh().await);
        assert!(connection.needs_reauthentication().await);
    }

    #[tokio::test]
    async fn test_authenticated_frame_retry_with_session_refresh_needed() {
        let mut connection = Connection::new("wss://example.com".to_string());

        // Create session that needs refresh
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = crate::client::session::EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Set last_seen to 8 days ago (should need refresh)
        session.last_seen = chrono::Utc::now() - chrono::Duration::days(8);

        let session_ref = Arc::new(Mutex::new(session));
        connection.set_session(session_ref);

        // Test send authenticated frame with retry - should fail due to session refresh needed
        let result = connection
            .send_authenticated_frame_with_retry(&[1, 2, 3])
            .await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Session needs refresh"));
            }
            _ => panic!("Expected Session error"),
        }

        // Test receive authenticated frame with retry - should fail due to session refresh needed
        let result = connection.receive_authenticated_frame_with_retry().await;
        assert!(result.is_err());
        match result.unwrap_err() {
            WhatsAppError::Session(msg) => {
                assert!(msg.contains("Session needs refresh"));
            }
            _ => panic!("Expected Session error"),
        }
    }
}
