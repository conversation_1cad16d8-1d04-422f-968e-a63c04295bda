{"enabled": true, "name": "Rust Documentation Sync", "description": "Listens to Rust source files and configuration files, then updates documentation in README or docs folder when changes occur", "version": "1", "when": {"type": "fileEdited", "patterns": ["src/**/*.rs", "Cargo.toml", "Cargo.lock"]}, "then": {"type": "askAgent", "prompt": "The Rust source code or project configuration has been modified. Please review the changes and update the documentation accordingly. If there's a README.md file, update it with any relevant changes to functionality, API, or usage. If there's a /docs folder, update the appropriate documentation files there. Focus on keeping the documentation accurate and up-to-date with the current codebase."}}