//! Message handling and Protocol Buffer operations

use crate::error::Result;
use crate::protocol::crypto::EncryptionManager;
use crate::protocol::proto::{message::ImageMessage, *};
use crate::types::{Event, IncomingMessage, Message, MessageContent, PhoneNumber};
use prost::Message as ProstMessage;
use std::time::{SystemTime, UNIX_EPOCH};

/// Handles Protocol Buffer message serialization/deserialization
pub struct MessageHandler {
    encryption: EncryptionManager,
}

impl MessageHandler {
    /// Create a new message handler
    pub fn new(encryption: EncryptionManager) -> Self {
        Self { encryption }
    }

    /// Serialize message to Protocol Buffer bytes
    pub fn serialize_message(&mut self, msg: &Message) -> Result<Vec<u8>> {
        let proto_message = self.convert_to_proto_message(msg)?;
        let recipient_jid = self.get_recipient_jid(msg);

        // Create WebMessageInfo wrapper
        let web_message_info = WebMessageInfo {
            key: Some(MessageKey {
                remote_jid: Some(recipient_jid.clone()),
                from_me: Some(true),
                id: Some(self.generate_message_id()),
                participant: None,
            }),
            message: Some(proto_message),
            message_timestamp: Some(
                SystemTime::now()
                    .duration_since(UNIX_EPOCH)
                    .unwrap()
                    .as_secs(),
            ),
            status: Some(1), // PENDING
            ..Default::default()
        };

        // Serialize to bytes
        let mut buffer = Vec::new();
        web_message_info.encode(&mut buffer)?;

        // Encrypt the serialized message
        self.encryption.encrypt_message(&recipient_jid, &buffer)
    }

    /// Deserialize Protocol Buffer data to message
    pub fn deserialize_message(&mut self, contact_id: &str, data: &[u8]) -> Result<Message> {
        // Decrypt the message first
        let decrypted_data = self.encryption.decrypt_message(contact_id, data)?;

        // Decode the Protocol Buffer message
        let web_message_info = WebMessageInfo::decode(&decrypted_data[..])?;

        // Convert proto message to our Message type
        if let Some(proto_message) = web_message_info.message {
            self.convert_from_proto_message(proto_message)
        } else {
            Err(crate::error::WhatsAppError::Protocol(
                "No message content found in WebMessageInfo".to_string(),
            ))
        }
    }

    /// Handle incoming message and convert to event (async version)
    pub async fn handle_incoming_message(&mut self, data: Vec<u8>) -> Result<Event> {
        // First, we need to extract the sender info to decrypt the message
        // For now, we'll use a placeholder contact_id - in a real implementation,
        // this would be extracted from the message metadata or connection context
        let contact_id = "unknown_sender";

        // Decrypt the incoming data
        let decrypted_data = self.encryption.decrypt_message(contact_id, &data)?;

        // Decode the Protocol Buffer message
        let web_message_info = WebMessageInfo::decode(&decrypted_data[..])?;

        // Extract message information
        let from = web_message_info
            .key
            .as_ref()
            .and_then(|k| k.remote_jid.clone())
            .unwrap_or_else(|| "unknown".to_string());

        let message_id = web_message_info.key.as_ref().and_then(|k| k.id.clone());

        let timestamp = web_message_info
            .message_timestamp
            .map(|ts| UNIX_EPOCH + std::time::Duration::from_secs(ts))
            .unwrap_or_else(SystemTime::now);

        // Convert message content
        let content = if let Some(proto_message) = web_message_info.message {
            self.convert_proto_to_message_content(proto_message)?
        } else {
            return Err(crate::error::WhatsAppError::Protocol(
                "No message content found".to_string(),
            ));
        };

        let incoming_message = IncomingMessage {
            from,
            content,
            timestamp,
            message_id,
        };

        Ok(Event::MessageReceived(incoming_message))
    }

    /// Convert our Message type to Protocol Buffer Message
    fn convert_to_proto_message(&self, msg: &Message) -> Result<ProtoMessage> {
        match msg {
            Message::Text { content, .. } => Ok(ProtoMessage {
                conversation: Some(content.clone()),
                ..Default::default()
            }),
            Message::Image {
                caption,
                mime_type,
                data,
                ..
            } => {
                Ok(ProtoMessage {
                    image_message: Some(Box::new(ImageMessage {
                        caption: caption.clone(),
                        mimetype: Some(mime_type.clone()),
                        file_sha256: Some(self.calculate_sha256(data)),
                        file_length: Some(data.len() as u64),
                        // Note: In a real implementation, you would upload the image
                        // to WhatsApp's servers and get a URL/media key
                        url: Some("placeholder_url".to_string()),
                        media_key: Some(vec![0u8; 32]), // Placeholder
                        ..Default::default()
                    })),
                    ..Default::default()
                })
            }
        }
    }

    /// Convert Protocol Buffer Message to our Message type
    fn convert_from_proto_message(&self, proto_msg: ProtoMessage) -> Result<Message> {
        if let Some(text) = proto_msg.conversation {
            // For text messages, we need a recipient. In a real implementation,
            // this would come from the message context or be handled differently
            let recipient = PhoneNumber::new("+1234567890")?; // Placeholder
            Ok(Message::Text {
                to: recipient,
                content: text,
            })
        } else if let Some(image_msg) = proto_msg.image_message {
            let recipient = PhoneNumber::new("+1234567890")?; // Placeholder
            Ok(Message::Image {
                to: recipient,
                data: vec![], // In real implementation, download from URL
                caption: image_msg.caption,
                mime_type: image_msg
                    .mimetype
                    .unwrap_or_else(|| "image/jpeg".to_string()),
            })
        } else {
            Err(crate::error::WhatsAppError::Protocol(
                "Unsupported message type".to_string(),
            ))
        }
    }

    /// Convert Protocol Buffer Message to MessageContent for incoming messages
    fn convert_proto_to_message_content(&self, proto_msg: ProtoMessage) -> Result<MessageContent> {
        if let Some(text) = proto_msg.conversation {
            Ok(MessageContent::Text(text))
        } else if let Some(image_msg) = proto_msg.image_message {
            Ok(MessageContent::Image {
                data: vec![], // In real implementation, download from URL
                caption: image_msg.caption,
            })
        } else {
            Err(crate::error::WhatsAppError::Protocol(
                "Unsupported message content type".to_string(),
            ))
        }
    }

    /// Get recipient JID from message
    fn get_recipient_jid(&self, msg: &Message) -> String {
        match msg {
            Message::Text { to, .. } | Message::Image { to, .. } => {
                format!("{}@s.whatsapp.net", to.as_str().trim_start_matches('+'))
            }
        }
    }

    /// Generate a unique message ID
    fn generate_message_id(&self) -> String {
        use rand::Rng;
        let mut rng = rand::rng();
        let id: u64 = rng.random();
        format!("{:016X}", id)
    }

    /// Calculate SHA256 hash of data
    fn calculate_sha256(&self, data: &[u8]) -> Vec<u8> {
        use sha2::{Digest, Sha256};
        let mut hasher = Sha256::new();
        hasher.update(data);
        hasher.finalize().to_vec()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::protocol::crypto::EncryptionManager;

    #[test]
    fn test_message_handler_creation() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);
        // Test that handler is created successfully
        assert!(std::ptr::addr_of!(handler).is_aligned());
    }

    #[test]
    fn test_convert_text_message_to_proto() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let phone = PhoneNumber::new("+1234567890").unwrap();
        let message = Message::Text {
            to: phone,
            content: "Hello, World!".to_string(),
        };

        let proto_message = handler.convert_to_proto_message(&message).unwrap();
        assert_eq!(
            proto_message.conversation,
            Some("Hello, World!".to_string())
        );
    }

    #[test]
    fn test_convert_image_message_to_proto() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let phone = PhoneNumber::new("+1234567890").unwrap();
        let message = Message::Image {
            to: phone,
            data: vec![1, 2, 3, 4],
            caption: Some("Test image".to_string()),
            mime_type: "image/png".to_string(),
        };

        let proto_message = handler.convert_to_proto_message(&message).unwrap();
        assert!(proto_message.image_message.is_some());

        let image_msg = proto_message.image_message.unwrap();
        assert_eq!(image_msg.caption, Some("Test image".to_string()));
        assert_eq!(image_msg.mimetype, Some("image/png".to_string()));
    }

    #[test]
    fn test_get_recipient_jid() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let phone = PhoneNumber::new("+1234567890").unwrap();
        let message = Message::Text {
            to: phone,
            content: "Test".to_string(),
        };

        let jid = handler.get_recipient_jid(&message);
        assert_eq!(jid, "<EMAIL>");
    }

    #[test]
    fn test_generate_message_id() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let id1 = handler.generate_message_id();
        let id2 = handler.generate_message_id();

        // IDs should be different
        assert_ne!(id1, id2);
        // IDs should be 16 characters long (64-bit hex)
        assert_eq!(id1.len(), 16);
        assert_eq!(id2.len(), 16);
    }

    #[test]
    fn test_calculate_sha256() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let data = b"Hello, World!";
        let hash = handler.calculate_sha256(data);

        // SHA256 hash should be 32 bytes
        assert_eq!(hash.len(), 32);

        // Same input should produce same hash
        let hash2 = handler.calculate_sha256(data);
        assert_eq!(hash, hash2);
    }

    #[test]
    fn test_convert_proto_to_message_content() {
        let encryption = EncryptionManager::new();
        let handler = MessageHandler::new(encryption);

        let proto_message = ProtoMessage {
            conversation: Some("Hello, World!".to_string()),
            ..Default::default()
        };

        let content = handler
            .convert_proto_to_message_content(proto_message)
            .unwrap();
        match content {
            MessageContent::Text(text) => assert_eq!(text, "Hello, World!"),
            _ => panic!("Expected text content"),
        }
    }
}
