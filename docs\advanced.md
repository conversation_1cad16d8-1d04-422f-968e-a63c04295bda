# Advanced Usage

## Custom Event Handler

```rust
use whatsmeow_rs::{<PERSON><PERSON><PERSON><PERSON>er, EventHandler, Event, Result};
use std::sync::Arc;
use std::time::Duration;

// Custom event handler
struct MyEventHandler;

#[async_trait::async_trait]
impl EventHandler for MyEventHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::MessageReceived(msg) => {
                println!("Received message from {}: {:?}", msg.from, msg.content);
            }
            Event::ConnectionStatusChanged(status) => {
                println!("Connection status: {:?}", status);
            }
            _ => {}
        }
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Create client with custom configuration
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(60))
        .reconnect_attempts(5)
        .session_file("my_session.json")
        .build()
        .await?;
    
    // Set event handler
    client.set_event_handler(Arc::new(MyEventHandler));
    
    // Connect and login
    client.connect().await?;
    client.login().await?;
    
    // Keep the client running to receive messages
    tokio::signal::ctrl_c().await.unwrap();
    client.disconnect().await?;
    Ok(())
}
```

## Dependencies for Advanced Usage

```toml
[dependencies]
whatsmeow-rs = "0.1"
tokio = { version = "1.0", features = ["full"] }
async-trait = "0.1"  # Required for custom event handlers
```