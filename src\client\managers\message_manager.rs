//! Message sending and handling

use crate::error::Result;
use crate::types::{Message, PhoneNumber};
use crate::protocol::crypto::EncryptionManager;
use crate::protocol::messages::MessageHandler;
use crate::client::connection::Connection;
use std::sync::Arc;
use tokio::sync::Mutex;
use rand::Rng;



pub struct MessageManager {
    connection: Arc<Mutex<Connection>>,
    message_handler: MessageHandler,
}

impl MessageManager {
    pub fn new(connection: Arc<Mutex<Connection>>) -> Self {
        let encryption_manager = EncryptionManager::new();
        let message_handler = MessageHandler::new(encryption_manager);

        Self {
            connection,
            message_handler,
        }
    }

    pub async fn send_text_message(
        &mut self,
        to: PhoneNumber,
        text: &str,
    ) -> Result<MessageDeliveryStatus> {
        let message = Message::Text {
            to: to.clone(),
            content: text.to_string(),
        };

        self.send_message_internal(message, to).await
    }

    pub async fn send_image_message(
        &mut self,
        to: PhoneNumber,
        image_data: Vec<u8>,
        caption: Option<String>,
        mime_type: String,
    ) -> Result<MessageDeliveryStatus> {
        let message = Message::Image {
            to: to.clone(),
            data: image_data,
            caption,
            mime_type,
        };

        self.send_message_internal(message, to).await
    }

    async fn send_message_internal(
        &mut self,
        message: Message,
        recipient: PhoneNumber,
    ) -> Result<MessageDeliveryStatus> {
        // Serialize and encrypt message
        let serialized_data = self.message_handler.serialize_message(&message)?;

        // Send through connection
        let mut connection = self.connection.lock().await;
        connection.send_authenticated_frame_with_retry(&serialized_data).await?;

        // Generate message ID and return status
        let message_id = self.generate_message_id();
        
        Ok(MessageDeliveryStatus {
            message_id,
            recipient,
            status: DeliveryStatus::Sent,
            timestamp: std::time::SystemTime::now(),
            error: None,
        })
    }

    fn generate_message_id(&self) -> String {
        let mut rng = rand::rng();
        let id: u64 = rng.random();
        format!("{:016X}", id)
    }
}