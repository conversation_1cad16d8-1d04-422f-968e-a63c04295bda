// Generated Protocol Buffer definitions for WhatsApp Web protocol
// This module contains the generated Rust structs from the wa.proto file

// Include the generated protobuf code
include!(concat!(env!("OUT_DIR"), "/waproto.rs"));

// Re-export commonly used types for convenience
pub use self::Message as ProtoMessage;

#[cfg(test)]
mod tests {
    use super::*;
    use prost::Message;

    #[test]
    fn test_protobuf_serialization() {
        // Test basic protobuf serialization/deserialization
        let client_payload = ClientPayload {
            username: Some(12345),
            passive: Some(false),
            push_name: Some("Test Client".to_string()),
            session_id: Some(1),
            short_connect: Some(true),
            ..Default::default()
        };

        // Serialize to bytes
        let serialized = client_payload.encode_to_vec();
        assert!(!serialized.is_empty());

        // Deserialize back
        let deserialized = ClientPayload::decode(&serialized[..]).unwrap();
        assert_eq!(deserialized.username, Some(12345));
        assert_eq!(deserialized.push_name, Some("Test Client".to_string()));
    }

    #[test]
    fn test_message_key_creation() {
        let message_key = MessageKey {
            remote_jid: Some("<EMAIL>".to_string()),
            from_me: Some(true),
            id: Some("test_message_id".to_string()),
            participant: None,
        };

        let serialized = message_key.encode_to_vec();
        assert!(!serialized.is_empty());

        let deserialized = MessageKey::decode(&serialized[..]).unwrap();
        assert_eq!(
            deserialized.remote_jid,
            Some("<EMAIL>".to_string())
        );
        assert_eq!(deserialized.from_me, Some(true));
        assert_eq!(deserialized.id, Some("test_message_id".to_string()));
    }
}
