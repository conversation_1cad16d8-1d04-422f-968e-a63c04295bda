//! Optimized synchronization primitives for client operations

use std::sync::atomic::{AtomicBool, AtomicU64, Ordering};
use std::sync::Arc;
use tokio::sync::{RwLock, Notify};
use crate::client::ConnectionState;

/// Lock-free connection state management
pub struct AtomicConnectionState {
    state: AtomicU64,
    notify: Arc<Notify>,
}

impl AtomicConnectionState {
    pub fn new() -> Self {
        Self {
            state: AtomicU64::new(ConnectionState::Disconnected as u64),
            notify: Arc::new(Notify::new()),
        }
    }

    pub fn get(&self) -> ConnectionState {
        let state_val = self.state.load(Ordering::Acquire);
        match state_val {
            0 => ConnectionState::Disconnected,
            1 => ConnectionState::Connecting,
            2 => ConnectionState::Connected,
            3 => ConnectionState::Reconnecting,
            4 => ConnectionState::Failed,
            _ => ConnectionState::Disconnected,
        }
    }

    pub fn set(&self, new_state: ConnectionState) {
        let state_val = new_state as u64;
        self.state.store(state_val, Ordering::Release);
        self.notify.notify_waiters();
    }

    pub async fn wait_for_change(&self) {
        self.notify.notified().await;
    }
}

/// Read-optimized session wrapper
pub struct SessionWrapper<T> {
    inner: Arc<RwLock<T>>,
    is_valid: AtomicBool,
    last_update: AtomicU64,
}

impl<T> SessionWrapper<T> {
    pub fn new(value: T) -> Self {
        Self {
            inner: Arc::new(RwLock::new(value)),
            is_valid: AtomicBool::new(false),
            last_update: AtomicU64::new(0),
        }
    }

    pub async fn read(&self) -> tokio::sync::RwLockReadGuard<'_, T> {
        self.inner.read().await
    }

    pub async fn write(&self) -> tokio::sync::RwLockWriteGuard<'_, T> {
        let now = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs();
        self.last_update.store(now, Ordering::Release);
        self.inner.write().await
    }

    pub fn is_valid(&self) -> bool {
        self.is_valid.load(Ordering::Acquire)
    }

    pub fn set_valid(&self, valid: bool) {
        self.is_valid.store(valid, Ordering::Release);
    }

    pub fn last_update_timestamp(&self) -> u64 {
        self.last_update.load(Ordering::Acquire)
    }
}