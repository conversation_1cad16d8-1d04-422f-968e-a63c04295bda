//! Message sending example
//!
//! This example demonstrates how to:
//! - Connect and authenticate with WhatsApp
//! - Send text messages to contacts
//! - Handle message delivery status
//! - Implement proper error handling
//!
//! Run with: cargo run --example send_message

use std::sync::Arc;
use std::time::Duration;
use tokio::io::{self, AsyncBufReadExt, BufReader};
use whatsmeow_rs::{
    ClientBuilder, Event, EventHandler, PhoneNumber, Result, WhatsAppClient, WhatsAppError,
};

/// Event handler that tracks message delivery
struct MessageEventHandler;

#[async_trait::async_trait]
impl EventHandler for MessageEventHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::QRCodeGenerated(qr_data) => {
                println!("📱 Please scan this QR code with WhatsApp:");
                println!("{:?}", qr_data);
            }
            Event::LoginSuccess => {
                println!("✅ Successfully authenticated with WhatsApp!");
            }
            Event::LoginFailure(reason) => {
                println!("❌ Authentication failed: {:?}", reason);
            }
            Event::ConnectionStatusChanged(status) => {
                println!("🔗 Connection: {:?}", status);
            }
            Event::MessageReceived(msg) => {
                println!("📨 Received from {}: {:?}", msg.from, msg.content);
            }
        }
        Ok(())
    }
}

async fn get_user_input(prompt: &str) -> Result<String> {
    println!("{}", prompt);
    let stdin = io::stdin();
    let mut reader = BufReader::new(stdin);
    let mut input = String::new();

    match reader.read_line(&mut input).await {
        Ok(_) => Ok(input.trim().to_string()),
        Err(e) => Err(WhatsAppError::Io(e)),
    }
}

async fn send_text_message(client: &WhatsAppClient) -> Result<()> {
    // Get recipient phone number
    let phone_input =
        get_user_input("📞 Enter recipient phone number (with country code, e.g., +1234567890):")
            .await?;

    let phone = match PhoneNumber::new(&phone_input) {
        Ok(p) => p,
        Err(e) => {
            println!("❌ Invalid phone number format: {}", e);
            return Ok(());
        }
    };

    // Get message content
    let message_text = get_user_input("💬 Enter your message:").await?;

    if message_text.is_empty() {
        println!("❌ Message cannot be empty");
        return Ok(());
    }

    // Send the message
    println!("📤 Sending message to {}...", phone);

    match client.send_message(phone.clone(), &message_text).await {
        Ok(delivery_status) => {
            println!("✅ Message sent successfully!");
            println!("📋 Delivery Status:");
            println!("  Message ID: {}", delivery_status.message_id);
            println!("  Recipient: {}", delivery_status.recipient);
            println!("  Status: {:?}", delivery_status.status);
            println!("  Timestamp: {:?}", delivery_status.timestamp);

            if let Some(error) = delivery_status.error {
                println!("  Error: {}", error);
            }

            // Demonstrate status checking
            match delivery_status.status {
                whatsmeow_rs::DeliveryStatus::Sent => {
                    println!("📡 Message has been sent to WhatsApp servers")
                }
                whatsmeow_rs::DeliveryStatus::Delivered => {
                    println!("📱 Message delivered to recipient's device")
                }
                whatsmeow_rs::DeliveryStatus::Read => {
                    println!("👁️  Message has been read by recipient")
                }
                whatsmeow_rs::DeliveryStatus::Failed => println!("❌ Message delivery failed"),
                whatsmeow_rs::DeliveryStatus::Pending => println!("⏳ Message is pending delivery"),
                whatsmeow_rs::DeliveryStatus::ServerAck => {
                    println!("🔄 Server acknowledged receipt")
                }
                whatsmeow_rs::DeliveryStatus::DeliveryAck => {
                    println!("✅ Delivery acknowledged by recipient's device")
                }
                whatsmeow_rs::DeliveryStatus::Played => {
                    println!("🔊 Message was played (voice message)")
                }
                whatsmeow_rs::DeliveryStatus::Error => println!("⚠️  Generic error occurred"),
            }
        }
        Err(WhatsAppError::Authentication(msg)) => {
            println!("❌ Authentication error: {}", msg);
            println!("💡 Try reconnecting and logging in again");
        }
        Err(WhatsAppError::NotConnected) => {
            println!("❌ Not connected to WhatsApp servers");
            println!("💡 Check your internet connection and try again");
        }
        Err(WhatsAppError::InvalidInput(msg)) => {
            println!("❌ Invalid input: {}", msg);
        }
        Err(e) => {
            println!("❌ Failed to send message: {}", e);
        }
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    println!("📱 WhatsApp Message Sender");
    println!("==========================");

    // Create client with custom configuration
    let mut client = ClientBuilder::new()
        .connect_timeout(Duration::from_secs(60))
        .reconnect_attempts(3)
        .session_file("message_sender_session.json")
        .build()
        .await?;

    // Set event handler
    let event_handler = Arc::new(MessageEventHandler);
    client.set_event_handler(event_handler);

    // Connect and authenticate
    println!("🔌 Connecting to WhatsApp...");
    client.connect().await?;

    println!("🔐 Authenticating...");
    client.login().await?;

    // Interactive message sending loop
    loop {
        println!("\n📋 Options:");
        println!("1. Send a text message");
        println!("2. Check session info");
        println!("3. Exit");

        let choice = get_user_input("Choose an option (1-3):").await?;

        match choice.as_str() {
            "1" => {
                if let Err(e) = send_text_message(&client).await {
                    println!("❌ Error sending message: {}", e);
                }
            }
            "2" => {
                if let Some(session_info) = client.get_session_info().await {
                    println!("📋 Session Information:");
                    println!("  Device ID: {}", session_info.device_id);
                    println!("  Authenticated: {}", session_info.is_authenticated);
                    println!("  Last seen: {:?}", session_info.last_seen);
                    if let Some(age) = session_info.age_seconds {
                        println!("  Session age: {} seconds", age);
                    } else {
                        println!("  Session age: unknown");
                    }
                    println!("  Needs refresh: {}", session_info.needs_refresh);
                } else {
                    println!("❌ No session information available");
                }
            }
            "3" => {
                println!("👋 Exiting...");
                break;
            }
            _ => {
                println!("❌ Invalid option. Please choose 1, 2, or 3.");
            }
        }
    }

    // Graceful shutdown
    println!("🛑 Disconnecting from WhatsApp...");
    client.disconnect().await?;
    println!("✅ Disconnected successfully. Goodbye!");

    Ok(())
}
