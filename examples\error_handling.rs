//! Error handling best practices example
//!
//! This example demonstrates:
//! - Comprehensive error handling patterns
//! - Recovery strategies for different error types
//! - Logging and debugging techniques
//! - Graceful degradation
//!
//! Run with: cargo run --example error_handling

use std::time::Duration;
use tokio::time::sleep;
use whatsmeow_rs::{ClientBuilder, PhoneNumber, Result, WhatsAppClient, WhatsAppError};

/// Demonstrate different error handling strategies
async fn demonstrate_error_handling() -> Result<()> {
    println!("🔧 Error Handling Demonstration");
    println!("===============================\n");

    // 1. Client Creation Errors
    println!("1️⃣  Testing client creation errors...");

    // Invalid configuration
    match ClientBuilder::new()
        .connect_timeout(Duration::from_secs(0)) // Invalid timeout
        .build()
        .await
    {
        Ok(_) => println!("❌ Expected error but got success"),
        Err(WhatsAppError::InvalidConfig(msg)) => {
            println!("✅ Caught expected config error: {}", msg);
        }
        Err(e) => println!("❌ Unexpected error type: {}", e),
    }

    // 2. Phone Number Validation Errors
    println!("\n2️⃣  Testing phone number validation...");

    let invalid_numbers = vec![
        "1234567890",            // Missing +
        "+123",                  // Too short
        "+123abc456",            // Contains letters
        "+12345678901234567890", // Too long
    ];

    for number in invalid_numbers {
        match PhoneNumber::new(number) {
            Ok(_) => println!("❌ Expected error for {}", number),
            Err(e) => println!("✅ Correctly rejected '{}': {}", number, e),
        }
    }

    // 3. Connection Errors with Retry Logic
    println!("\n3️⃣  Testing connection with retry logic...");

    let mut client = ClientBuilder::new()
        .server_url("wss://invalid-server.example.com") // Invalid server
        .connect_timeout(Duration::from_secs(5))
        .reconnect_attempts(2)
        .build()
        .await?;

    // Attempt connection with retry
    let max_retries = 3;
    let mut retry_count = 0;

    loop {
        match client.connect().await {
            Ok(_) => {
                println!("✅ Connected successfully");
                break;
            }
            Err(WhatsAppError::Connection(e)) => {
                retry_count += 1;
                if retry_count >= max_retries {
                    println!("❌ Connection failed after {} retries: {}", max_retries, e);
                    break;
                } else {
                    println!("⚠️  Connection attempt {} failed: {}", retry_count, e);
                    println!("🔄 Retrying in 2 seconds...");
                    sleep(Duration::from_secs(2)).await;
                }
            }
            Err(WhatsAppError::Timeout { timeout }) => {
                println!("⏰ Connection timed out after {:?}", timeout);
                break;
            }
            Err(e) => {
                println!("❌ Unexpected connection error: {}", e);
                break;
            }
        }
    }

    Ok(())
}

/// Demonstrate message sending error handling
async fn demonstrate_message_errors(client: &WhatsAppClient) -> Result<()> {
    println!("\n4️⃣  Testing message sending errors...");

    // Test sending without authentication
    let phone = PhoneNumber::new("+1234567890")?;

    match client.send_message(phone.clone(), "Test message").await {
        Ok(status) => {
            println!("✅ Message sent: {}", status.message_id);
        }
        Err(WhatsAppError::Authentication(msg)) => {
            println!("✅ Correctly caught auth error: {}", msg);
        }
        Err(WhatsAppError::NotConnected) => {
            println!("✅ Correctly caught not connected error");
        }
        Err(e) => {
            println!("⚠️  Other error: {}", e);
        }
    }

    // Test invalid message content
    let empty_message = "";
    match client.send_message(phone.clone(), empty_message).await {
        Ok(_) => println!("❌ Expected error for empty message"),
        Err(WhatsAppError::InvalidInput(msg)) => {
            println!("✅ Correctly rejected empty message: {}", msg);
        }
        Err(e) => println!("⚠️  Unexpected error: {}", e),
    }

    Ok(())
}

/// Demonstrate session error handling
async fn demonstrate_session_errors(client: &WhatsAppClient) -> Result<()> {
    println!("\n5️⃣  Testing session management errors...");

    // Check session status
    match client.get_session_info().await {
        Some(info) => {
            println!("📋 Session info available:");
            println!("  Device ID: {}", info.device_id);
            println!("  Authenticated: {}", info.is_authenticated);
            println!("  Needs refresh: {}", info.needs_refresh);
        }
        None => {
            println!("⚠️  No session information available");
        }
    }

    // Test operations that require authentication
    if !client.has_valid_session().await {
        println!("⚠️  No valid session - operations will fail gracefully");
    }

    Ok(())
}

/// Demonstrate error recovery patterns
async fn demonstrate_recovery_patterns() -> Result<()> {
    println!("\n6️⃣  Testing error recovery patterns...");

    // Circuit breaker pattern simulation
    let mut failure_count = 0;
    let max_failures = 3;
    let mut circuit_open = false;

    for attempt in 1..=5 {
        if circuit_open {
            println!("🚫 Circuit breaker open - skipping attempt {}", attempt);
            continue;
        }

        // Simulate operation that might fail
        let success = attempt > 3; // Succeed after 3 attempts

        if success {
            println!("✅ Operation {} succeeded", attempt);
            failure_count = 0; // Reset failure count
            circuit_open = false;
        } else {
            failure_count += 1;
            println!(
                "❌ Operation {} failed (failure count: {})",
                attempt, failure_count
            );

            if failure_count >= max_failures {
                println!("🚫 Circuit breaker opened after {} failures", max_failures);
                circuit_open = true;
            }
        }

        sleep(Duration::from_millis(500)).await;
    }

    Ok(())
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging with error level
    tracing_subscriber::fmt()
        .with_max_level(tracing::Level::INFO)
        .init();

    println!("🛡️  WhatsApp Error Handling Examples");
    println!("====================================");

    // Run error handling demonstrations
    if let Err(e) = demonstrate_error_handling().await {
        println!("❌ Error in demonstration: {}", e);
    }

    // Create a valid client for further testing
    println!("\n🔧 Creating client for additional tests...");
    let client = match WhatsAppClient::new().await {
        Ok(c) => c,
        Err(e) => {
            println!("❌ Failed to create client: {}", e);
            return Err(e);
        }
    };

    // Test message errors
    if let Err(e) = demonstrate_message_errors(&client).await {
        println!("❌ Error in message demonstration: {}", e);
    }

    // Test session errors
    if let Err(e) = demonstrate_session_errors(&client).await {
        println!("❌ Error in session demonstration: {}", e);
    }

    // Test recovery patterns
    if let Err(e) = demonstrate_recovery_patterns().await {
        println!("❌ Error in recovery demonstration: {}", e);
    }

    println!("\n✅ Error handling demonstration completed!");
    println!("\n💡 Key Takeaways:");
    println!("  • Always handle specific error types appropriately");
    println!("  • Implement retry logic with exponential backoff");
    println!("  • Use circuit breaker patterns for external services");
    println!("  • Log errors appropriately for debugging");
    println!("  • Provide meaningful error messages to users");
    println!("  • Implement graceful degradation when possible");

    Ok(())
}
