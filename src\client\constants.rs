//! Constants for client configuration and limits

use std::time::Duration;

/// Event channel buffer size for broadcasting
pub const EVENT_CHANNEL_BUFFER_SIZE: usize = 100;

/// Timeout for receiving messages in the listener loop
pub const MESSAGE_RECEIVE_TIMEOUT: Duration = Duration::from_secs(5);

/// Delay between retry attempts
pub const RETRY_DELAY: Duration = Duration::from_millis(100);

/// Maximum message content length (64KB)
pub const MAX_MESSAGE_LENGTH: usize = 65536;

/// Maximum image size (16MB)
pub const MAX_IMAGE_SIZE: usize = 16 * 1024 * 1024;

/// Supported image MIME types
pub const SUPPORTED_IMAGE_TYPES: &[&str] = &[
    "image/jpeg",
    "image/png", 
    "image/gif",
    "image/webp",
];

/// Phone number validation constants
pub const MIN_PHONE_LENGTH: usize = 8;  // +1234567
pub const MAX_PHONE_LENGTH: usize = 16; // +123456789012345

/// Session save interval for batching
pub const SESSION_SAVE_INTERVAL: Duration = Duration::from_secs(30);

/// Background task check interval
pub const BACKGROUND_TASK_INTERVAL: Duration = Duration::from_secs(10);

/// Connection timeout limits
pub const MIN_CONNECT_TIMEOUT: Duration = Duration::from_secs(1);
pub const MAX_CONNECT_TIMEOUT: Duration = Duration::from_secs(300);

/// Reconnection attempt limits
pub const MIN_RECONNECT_ATTEMPTS: u32 = 1;
pub const MAX_RECONNECT_ATTEMPTS: u32 = 100;