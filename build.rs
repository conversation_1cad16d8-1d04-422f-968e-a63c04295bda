use std::env;
use std::path::PathBuf;

const PROTO_DIR: &str = "proto";
const PROTO_FILES: &[&str] = &["proto/wa.proto"];
const MIN_PROTO_FILE_SIZE: u64 = 1;

type BuildResult<T> = Result<T, Box<dyn std::error::Error>>;

fn main() -> BuildResult<()> {
    setup_build_tracking();

    let out_dir = PathBuf::from(env::var("OUT_DIR")?);
    let valid_protos = validate_proto_files(PROTO_FILES);

    if valid_protos.is_empty() {
        println!("cargo:warning=No valid protocol buffer files found");
        return Ok(());
    }

    compile_proto_files(&valid_protos, &out_dir)?;

    println!(
        "cargo:warning=Successfully compiled {} protocol buffer files",
        valid_protos.len()
    );

    Ok(())
}

fn setup_build_tracking() {
    println!("cargo:rerun-if-changed=build.rs");
    for &proto_file in PROTO_FILES {
        println!("cargo:rerun-if-changed={}", proto_file);
    }
}

fn validate_proto_files<'a>(files: &'a [&'a str]) -> Vec<&'a str> {
    files
        .iter()
        .filter(|&&file| is_valid_proto_file(file))
        .copied()
        .collect()
}

fn is_valid_proto_file(file_path: &str) -> bool {
    let path = std::path::Path::new(file_path);
    path.exists()
        && path
            .metadata()
            .map(|m| m.len() >= MIN_PROTO_FILE_SIZE)
            .unwrap_or(false)
}

fn compile_proto_files(valid_protos: &[&str], out_dir: &PathBuf) -> BuildResult<()> {
    prost_build::Config::new()
        .out_dir(out_dir)
        .compile_protos(valid_protos, &[PROTO_DIR])
        .map_err(|e| {
            eprintln!("Failed to compile protocol buffers: {}", e);
            eprintln!("Attempted files: {:?}", valid_protos);
            print_installation_help();
            e.into()
        })
}

fn print_installation_help() {
    eprintln!("Protocol Buffer Compiler (protoc) installation required:");
    eprintln!("  Ubuntu/Debian: sudo apt install protobuf-compiler");
    eprintln!("  macOS: brew install protobuf");
    eprintln!("  Windows: Download from https://github.com/protocolbuffers/protobuf/releases");
}
