//! Configuration constants and defaults for WhatsApp client
//! 
//! This module contains all configurable parameters used throughout the library,
//! organized by functional area for easy maintenance and customization.

use std::time::Duration;

// =============================================================================
// Network Configuration
// =============================================================================

/// Default WhatsApp Web server WebSocket URL
pub const DEFAULT_WS_URL: &str = "wss://web.whatsapp.com/ws/chat";

/// Default timeout for initial connection establishment
pub const DEFAULT_CONNECT_TIMEOUT: Duration = Duration::from_secs(30);

/// Interval between ping messages to keep connection alive
pub const DEFAULT_PING_INTERVAL: Duration = Duration::from_secs(30);

// =============================================================================
// Reconnection Strategy
// =============================================================================

/// Maximum number of automatic reconnection attempts
pub const DEFAULT_MAX_RECONNECT_ATTEMPTS: u32 = 5;

/// Initial delay before first reconnection attempt
pub const BASE_RECONNECT_DELAY: Duration = Duration::from_secs(1);

/// Maximum delay between reconnection attempts (exponential backoff cap)
pub const MAX_RECONNECT_DELAY: Duration = Duration::from_secs(60);

// =============================================================================
// Session Management
// =============================================================================

/// Number of days before a session expires and requires re-authentication
pub const SESSION_EXPIRY_DAYS: u64 = 30;

/// Number of days before session refresh is recommended
pub const SESSION_REFRESH_DAYS: u64 = 7;

/// Size in bytes for device identifier generation
pub const DEVICE_ID_BYTES: usize = 16;

// =============================================================================
// Phone Number Validation (E.164 Format)
// =============================================================================

/// Minimum length for a valid phone number (including country code)
pub const MIN_PHONE_LENGTH: usize = 8;

/// Maximum length for a valid phone number in E.164 format
pub const MAX_PHONE_LENGTH: usize = 15;

// =============================================================================
// Cryptographic Parameters
// =============================================================================

/// Standard key size for symmetric encryption operations
pub const KEY_SIZE: usize = 32;

/// Key size for Noise protocol handshake
pub const NOISE_KEY_SIZE: usize = 32;

/// Size of identity keys used in Signal protocol
pub const IDENTITY_KEY_SIZE: usize = 32;

/// Size of signed pre-keys for forward secrecy
pub const SIGNED_PRE_KEY_SIZE: usize = 32;
