//! Session management with caching and batched operations

use crate::client::session::Session;
use crate::error::Result;
use std::path::Path;
use std::sync::Arc;
use std::time::{Duration, Instant};
use tokio::sync::Mutex;

/// Manages session persistence with caching and batching
pub struct SessionManager {
    session: Arc<Mutex<Session>>,
    session_file_path: Option<String>,
    last_save: Arc<Mutex<Option<Instant>>>,
    save_interval: Duration,
    pending_save: Arc<Mutex<bool>>,
}

impl SessionManager {
    pub fn new(session_file_path: Option<String>) -> Self {
        Self {
            session: Arc::new(Mutex::new(Session::new())),
            session_file_path,
            last_save: Arc::new(Mutex::new(None)),
            save_interval: Duration::from_secs(30), // Batch saves every 30 seconds
            pending_save: Arc::new(Mutex::new(false)),
        }
    }

    /// Load session from file if configured
    pub async fn load_session(&self) -> Result<()> {
        if let Some(session_path) = &self.session_file_path {
            match Session::load_from_file(Path::new(session_path)) {
                Ok(loaded_session) => {
                    *self.session.lock().await = loaded_session;
                    tracing::info!("Loaded existing session from {}", session_path);
                }
                Err(e) => {
                    tracing::warn!("Failed to load session from {}: {}", session_path, e);
                }
            }
        }
        Ok(())
    }

    /// Mark session for saving (batched)
    pub async fn mark_for_save(&self) {
        *self.pending_save.lock().await = true;
    }

    /// Save session immediately if needed
    pub async fn save_if_needed(&self) -> Result<()> {
        let should_save = {
            let pending = *self.pending_save.lock().await;
            let last_save = *self.last_save.lock().await;
            
            pending && (last_save.is_none() || 
                       last_save.unwrap().elapsed() >= self.save_interval)
        };

        if should_save {
            self.force_save().await?;
        }

        Ok(())
    }

    /// Force immediate save
    pub async fn force_save(&self) -> Result<()> {
        if let Some(session_path) = &self.session_file_path {
            let session = self.session.lock().await;
            if let Err(e) = session.save_to_file(Path::new(session_path)) {
                tracing::warn!("Failed to save session to {}: {}", session_path, e);
            } else {
                *self.last_save.lock().await = Some(Instant::now());
                *self.pending_save.lock().await = false;
                tracing::debug!("Session saved to {}", session_path);
            }
        }
        Ok(())
    }

    /// Get session reference
    pub fn session(&self) -> Arc<Mutex<Session>> {
        self.session.clone()
    }

    /// Check if session is valid without locking
    pub async fn is_valid(&self) -> bool {
        let session = self.session.lock().await;
        session.is_valid()
    }

    /// Start background save task
    pub fn start_background_save_task(&self) -> tokio::task::JoinHandle<()> {
        let session_manager = SessionManager {
            session: self.session.clone(),
            session_file_path: self.session_file_path.clone(),
            last_save: self.last_save.clone(),
            save_interval: self.save_interval,
            pending_save: self.pending_save.clone(),
        };

        tokio::spawn(async move {
            let mut interval = tokio::time::interval(Duration::from_secs(10));
            loop {
                interval.tick().await;
                if let Err(e) = session_manager.save_if_needed().await {
                    tracing::error!("Background session save failed: {}", e);
                }
            }
        })
    }
}