//! Session state management

use crate::error::{Result, WhatsAppError};
use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use std::fs;
use std::path::Path;

// Session expiration and refresh constants
const SESSION_EXPIRY_DAYS: u64 = 30;
const SESSION_REFRESH_DAYS: u64 = 7;
const DEVICE_ID_BYTES: usize = 16;

/// Session state for WhatsApp connection
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Session {
    pub device_id: String,
    pub client_token: String,
    pub server_token: String,
    pub encryption_keys: EncryptionKeys,
    pub last_seen: DateTime<Utc>,
    pub created_at: DateTime<Utc>,
    pub is_authenticated: bool,
}

/// Encryption keys for the session
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EncryptionKeys {
    pub noise_key: Vec<u8>,
    pub identity_key: Vec<u8>,
    pub signed_pre_key: Vec<u8>,
    pub registration_id: u32,
}

impl Default for EncryptionKeys {
    fn default() -> Self {
        Self {
            noise_key: Vec::new(),
            identity_key: Vec::new(),
            signed_pre_key: Vec::new(),
            registration_id: 0,
        }
    }
}

impl Session {
    /// Create a new session with generated device ID
    pub fn new() -> Self {
        let device_id = Self::generate_device_id();
        let now = Utc::now();

        Self {
            device_id,
            client_token: String::new(),
            server_token: String::new(),
            encryption_keys: EncryptionKeys::default(),
            last_seen: now,
            created_at: now,
            is_authenticated: false,
        }
    }

    /// Create a new session with specific device ID
    pub fn with_device_id(device_id: String) -> Self {
        let now = Utc::now();

        Self {
            device_id,
            client_token: String::new(),
            server_token: String::new(),
            encryption_keys: EncryptionKeys::default(),
            last_seen: now,
            created_at: now,
            is_authenticated: false,
        }
    }

    /// Generate a random device ID
    fn generate_device_id() -> String {
        use base64::Engine;
        use rand::RngCore;
        let mut rng = rand::rng();
        let mut random_bytes = [0u8; DEVICE_ID_BYTES];
        rng.fill_bytes(&mut random_bytes);
        base64::engine::general_purpose::STANDARD.encode(random_bytes)
    }

    /// Update the last seen timestamp
    pub fn update_last_seen(&mut self) {
        self.last_seen = Utc::now();
    }

    /// Mark session as authenticated
    pub fn set_authenticated(&mut self, client_token: String, server_token: String) {
        self.client_token = client_token;
        self.server_token = server_token;
        self.is_authenticated = true;
        self.update_last_seen();
    }

    /// Set encryption keys
    pub fn set_encryption_keys(&mut self, keys: EncryptionKeys) {
        self.encryption_keys = keys;
    }

    /// Save session to file
    pub fn save_to_file(&self, path: &Path) -> Result<()> {
        // Create parent directories if they don't exist
        if let Some(parent) = path.parent() {
            fs::create_dir_all(parent).map_err(|e| {
                WhatsAppError::Session(format!("Failed to create session directory: {}", e))
            })?;
        }

        // Serialize session to JSON
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| WhatsAppError::Session(format!("Failed to serialize session: {}", e)))?;

        // Write to file
        fs::write(path, json)
            .map_err(|e| WhatsAppError::Session(format!("Failed to write session file: {}", e)))?;

        tracing::debug!("Session saved to {:?}", path);
        Ok(())
    }

    /// Load session from file
    pub fn load_from_file(path: &Path) -> Result<Self> {
        if !path.exists() {
            return Err(WhatsAppError::Session(format!(
                "Session file does not exist: {:?}",
                path
            )));
        }

        // Read file contents
        let contents = fs::read_to_string(path)
            .map_err(|e| WhatsAppError::Session(format!("Failed to read session file: {}", e)))?;

        // Deserialize from JSON
        let session: Session = serde_json::from_str(&contents)
            .map_err(|e| WhatsAppError::Session(format!("Failed to deserialize session: {}", e)))?;

        tracing::debug!("Session loaded from {:?}", path);
        Ok(session)
    }

    /// Check if session is valid
    pub fn is_valid(&self) -> bool {
        if !self.has_device_id() {
            return false;
        }

        if !self.is_authenticated_with_tokens() {
            return false;
        }

        if self.is_expired() {
            return false;
        }

        if !self.has_encryption_keys() {
            return false;
        }

        true
    }

    /// Check if session has a valid device ID
    fn has_device_id(&self) -> bool {
        if self.device_id.is_empty() {
            tracing::debug!("Session invalid: missing device_id");
            false
        } else {
            true
        }
    }

    /// Check if session is authenticated with both tokens
    fn is_authenticated_with_tokens(&self) -> bool {
        if !self.is_authenticated {
            tracing::debug!("Session invalid: not authenticated");
            return false;
        }

        if self.client_token.is_empty() || self.server_token.is_empty() {
            tracing::debug!("Session invalid: missing tokens");
            return false;
        }

        true
    }

    /// Check if session is expired
    fn is_expired(&self) -> bool {
        let now = Utc::now();
        let duration = now.signed_duration_since(self.last_seen);

        if duration.num_seconds() > (SESSION_EXPIRY_DAYS * 24 * 60 * 60) as i64 {
            tracing::debug!("Session invalid: expired");
            true
        } else {
            false
        }
    }

    /// Check if session has all required encryption keys
    fn has_encryption_keys(&self) -> bool {
        if self.encryption_keys.noise_key.is_empty()
            || self.encryption_keys.identity_key.is_empty()
            || self.encryption_keys.signed_pre_key.is_empty()
        {
            tracing::debug!("Session invalid: missing encryption keys");
            false
        } else {
            true
        }
    }

    /// Check if session needs refresh (older than 7 days)
    pub fn needs_refresh(&self) -> bool {
        let now = Utc::now();
        let duration = now.signed_duration_since(self.last_seen);
        duration.num_seconds() > (SESSION_REFRESH_DAYS * 24 * 60 * 60) as i64
    }

    /// Get session age in seconds
    pub fn age_seconds(&self) -> Option<u64> {
        let now = Utc::now();
        let duration = now.signed_duration_since(self.created_at);
        Some(duration.num_seconds().max(0) as u64)
    }

    /// Clear authentication data (for logout)
    pub fn clear_auth(&mut self) {
        self.client_token.clear();
        self.server_token.clear();
        self.is_authenticated = false;
        self.encryption_keys = EncryptionKeys::default();
    }
}

impl Default for Session {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::Duration;
    use tempfile::NamedTempFile;

    #[test]
    fn test_session_creation() {
        let session = Session::new();

        assert!(!session.device_id.is_empty());
        assert!(session.client_token.is_empty());
        assert!(session.server_token.is_empty());
        assert!(!session.is_authenticated);
        assert!(session.encryption_keys.noise_key.is_empty());
        assert!(session.encryption_keys.identity_key.is_empty());
        assert!(session.encryption_keys.signed_pre_key.is_empty());
        assert_eq!(session.encryption_keys.registration_id, 0);
    }

    #[test]
    fn test_session_with_device_id() {
        let device_id = "test_device_123".to_string();
        let session = Session::with_device_id(device_id.clone());

        assert_eq!(session.device_id, device_id);
        assert!(!session.is_authenticated);
    }

    #[test]
    fn test_session_authentication() {
        let mut session = Session::new();
        let client_token = "client_token_123".to_string();
        let server_token = "server_token_456".to_string();

        assert!(!session.is_authenticated);

        session.set_authenticated(client_token.clone(), server_token.clone());

        assert!(session.is_authenticated);
        assert_eq!(session.client_token, client_token);
        assert_eq!(session.server_token, server_token);
    }

    #[test]
    fn test_session_encryption_keys() {
        let mut session = Session::new();
        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };

        session.set_encryption_keys(keys.clone());

        assert_eq!(session.encryption_keys.noise_key, keys.noise_key);
        assert_eq!(session.encryption_keys.identity_key, keys.identity_key);
        assert_eq!(session.encryption_keys.signed_pre_key, keys.signed_pre_key);
        assert_eq!(
            session.encryption_keys.registration_id,
            keys.registration_id
        );
    }

    #[test]
    fn test_session_validity() {
        let mut session = Session::new();

        // New session should be invalid (not authenticated)
        assert!(!session.is_valid());

        // Set authentication
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        // Still invalid without encryption keys
        assert!(!session.is_valid());

        // Add encryption keys
        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Now should be valid
        assert!(session.is_valid());
    }

    #[test]
    fn test_session_expiry() {
        let mut session = Session::new();

        // Set authentication and keys
        session.set_authenticated("client_token".to_string(), "server_token".to_string());
        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Should be valid when fresh
        assert!(session.is_valid());

        // Simulate old session (31 days ago)
        session.last_seen = Utc::now() - chrono::Duration::days(31);

        // Should be invalid due to expiry
        assert!(!session.is_valid());
    }

    #[test]
    fn test_session_needs_refresh() {
        let mut session = Session::new();

        // Fresh session doesn't need refresh
        assert!(!session.needs_refresh());

        // Session older than 7 days needs refresh
        session.last_seen = Utc::now() - chrono::Duration::days(8);
        assert!(session.needs_refresh());
    }

    #[test]
    fn test_session_clear_auth() {
        let mut session = Session::new();

        // Set authentication and keys
        session.set_authenticated("client_token".to_string(), "server_token".to_string());
        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        assert!(session.is_authenticated);
        assert!(!session.client_token.is_empty());
        assert!(!session.server_token.is_empty());

        // Clear authentication
        session.clear_auth();

        assert!(!session.is_authenticated);
        assert!(session.client_token.is_empty());
        assert!(session.server_token.is_empty());
        assert!(session.encryption_keys.noise_key.is_empty());
        assert!(session.encryption_keys.identity_key.is_empty());
        assert!(session.encryption_keys.signed_pre_key.is_empty());
        assert_eq!(session.encryption_keys.registration_id, 0);
    }

    #[test]
    fn test_session_save_and_load() {
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Create temporary file
        let temp_file = NamedTempFile::new().unwrap();
        let temp_path = temp_file.path();

        // Save session
        session.save_to_file(temp_path).unwrap();

        // Load session
        let loaded_session = Session::load_from_file(temp_path).unwrap();

        // Verify loaded session matches original
        assert_eq!(loaded_session.device_id, session.device_id);
        assert_eq!(loaded_session.client_token, session.client_token);
        assert_eq!(loaded_session.server_token, session.server_token);
        assert_eq!(loaded_session.is_authenticated, session.is_authenticated);
        assert_eq!(
            loaded_session.encryption_keys.noise_key,
            session.encryption_keys.noise_key
        );
        assert_eq!(
            loaded_session.encryption_keys.identity_key,
            session.encryption_keys.identity_key
        );
        assert_eq!(
            loaded_session.encryption_keys.signed_pre_key,
            session.encryption_keys.signed_pre_key
        );
        assert_eq!(
            loaded_session.encryption_keys.registration_id,
            session.encryption_keys.registration_id
        );
    }

    #[test]
    fn test_session_load_nonexistent_file() {
        let result = Session::load_from_file(Path::new("/nonexistent/path/session.json"));
        assert!(result.is_err());

        if let Err(WhatsAppError::Session(msg)) = result {
            assert!(msg.contains("Session file does not exist"));
        } else {
            panic!("Expected Session error");
        }
    }

    #[test]
    fn test_session_age() {
        let session = Session::new();
        let age = session.age_seconds();

        assert!(age.is_some());
        assert!(age.unwrap() < 5); // Should be very recent
    }

    #[test]
    fn test_update_last_seen() {
        let mut session = Session::new();
        let original_time = session.last_seen;

        // Wait a small amount to ensure time difference
        std::thread::sleep(Duration::from_millis(10));

        session.update_last_seen();

        assert!(session.last_seen > original_time);
    }

    #[test]
    fn test_generate_device_id() {
        let id1 = Session::generate_device_id();
        let id2 = Session::generate_device_id();

        // Device IDs should be different
        assert_ne!(id1, id2);

        // Should be valid base64
        use base64::Engine;
        assert!(
            base64::engine::general_purpose::STANDARD
                .decode(&id1)
                .is_ok()
        );
        assert!(
            base64::engine::general_purpose::STANDARD
                .decode(&id2)
                .is_ok()
        );

        // Should have expected length (16 bytes base64 encoded)
        let decoded = base64::engine::general_purpose::STANDARD
            .decode(&id1)
            .unwrap();
        assert_eq!(decoded.len(), DEVICE_ID_BYTES);
    }

    #[test]
    fn test_encryption_keys_default() {
        let keys = EncryptionKeys::default();

        assert!(keys.noise_key.is_empty());
        assert!(keys.identity_key.is_empty());
        assert!(keys.signed_pre_key.is_empty());
        assert_eq!(keys.registration_id, 0);
    }
}
