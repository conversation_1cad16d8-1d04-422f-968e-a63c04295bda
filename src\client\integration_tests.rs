//! Integration tests for session management with connection handling

#[cfg(test)]
mod tests {
    use super::super::{ClientConfig, SessionInfo, WhatsAppClient};
    use crate::client::session::{EncryptionKeys, Session};
    use std::time::Duration;
    use tempfile::NamedTempFile;

    #[tokio::test]
    async fn test_client_with_session_file() {
        // Create temporary session file
        let temp_file = NamedTempFile::new().unwrap();
        let session_path = temp_file.path().to_string_lossy().to_string();

        // Create client with session file path
        let config = ClientConfig {
            server_url: "wss://example.com".to_string(),
            connect_timeout: Duration::from_secs(30),
            reconnect_attempts: 3,
            session_file_path: Some(session_path.clone()),
        };

        let client = WhatsAppClient::with_config(config).await.unwrap();

        // Initially should not have valid session
        assert!(!client.has_valid_session().await);
        assert!(client.needs_reauthentication().await);
        assert!(client.get_session_info().await.is_none());
    }

    #[tokio::test]
    async fn test_client_load_existing_session() {
        // Create a valid session and save it to file
        let mut session = Session::new();
        session.set_authenticated("client_token".to_string(), "server_token".to_string());

        let keys = EncryptionKeys {
            noise_key: vec![1, 2, 3, 4],
            identity_key: vec![5, 6, 7, 8],
            signed_pre_key: vec![9, 10, 11, 12],
            registration_id: 12345,
        };
        session.set_encryption_keys(keys);

        // Save session to temporary file
        let temp_file = NamedTempFile::new().unwrap();
        let session_path = temp_file.path();
        session.save_to_file(session_path).unwrap();

        // Create client that should load the existing session
        let config = ClientConfig {
            server_url: "wss://example.com".to_string(),
            connect_timeout: Duration::from_secs(30),
            reconnect_attempts: 3,
            session_file_path: Some(session_path.to_string_lossy().to_string()),
        };

        let client = WhatsAppClient::with_config(config).await.unwrap();

        // Should have valid session loaded
        assert!(client.has_valid_session().await);
        assert!(!client.needs_reauthentication().await);

        let session_info = client.get_session_info().await.unwrap();
        assert_eq!(session_info.device_id, session.device_id);
        assert!(session_info.is_authenticated);
        assert!(!session_info.needs_refresh);
    }

    #[tokio::test]
    async fn test_client_session_info() {
        let client = WhatsAppClient::new().await.unwrap();

        // Initially no valid session info
        assert!(client.get_session_info().await.is_none());

        // Manually set up a valid session for testing
        {
            let session_ref = client.session.clone();
            let mut session = session_ref.lock().await;
            session.set_authenticated("test_client".to_string(), "test_server".to_string());

            let keys = EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
            session.set_encryption_keys(keys);
        }

        // Now should have valid session info
        let session_info = client.get_session_info().await.unwrap();
        assert!(session_info.is_authenticated);
        assert!(!session_info.device_id.is_empty());
        assert!(session_info.age_seconds.is_some());
        assert!(!session_info.needs_refresh);
    }

    #[tokio::test]
    async fn test_client_session_refresh_needed() {
        let client = WhatsAppClient::new().await.unwrap();

        // Set up session that needs refresh (old last_seen)
        {
            let session_ref = client.session.clone();
            let mut session = session_ref.lock().await;
            session.set_authenticated("test_client".to_string(), "test_server".to_string());

            let keys = EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
            session.set_encryption_keys(keys);

            // Set last_seen to 8 days ago
            session.last_seen = chrono::Utc::now() - chrono::Duration::days(8);
        }

        // Should have valid session but need refresh
        assert!(client.has_valid_session().await);
        assert!(client.session_needs_refresh().await);
        assert!(client.needs_reauthentication().await);

        let session_info = client.get_session_info().await.unwrap();
        assert!(session_info.is_authenticated);
        assert!(session_info.needs_refresh);
    }

    #[tokio::test]
    async fn test_client_logout() {
        let client = WhatsAppClient::new().await.unwrap();

        // Set up authenticated session
        {
            let session_ref = client.session.clone();
            let mut session = session_ref.lock().await;
            session.set_authenticated("test_client".to_string(), "test_server".to_string());

            let keys = EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
            session.set_encryption_keys(keys);
        }

        // Verify session is valid
        assert!(client.has_valid_session().await);
        assert!(!client.needs_reauthentication().await);

        // Logout should clear session
        let mut client = client; // Make mutable for logout
        client.logout().await.unwrap();

        // Session should be cleared
        assert!(!client.has_valid_session().await);
        assert!(client.needs_reauthentication().await);
        assert!(client.get_session_info().await.is_none());
    }

    #[tokio::test]
    async fn test_client_session_persistence() {
        let temp_file = NamedTempFile::new().unwrap();
        let session_path = temp_file.path().to_string_lossy().to_string();

        // Create client with session file
        let config = ClientConfig {
            server_url: "wss://example.com".to_string(),
            connect_timeout: Duration::from_secs(30),
            reconnect_attempts: 3,
            session_file_path: Some(session_path.clone()),
        };

        let mut client = WhatsAppClient::with_config(config.clone()).await.unwrap();

        // Set up authenticated session
        let device_id = {
            let session_ref = client.session.clone();
            let mut session = session_ref.lock().await;
            session.set_authenticated("test_client".to_string(), "test_server".to_string());

            let keys = EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
            session.set_encryption_keys(keys);
            session.device_id.clone()
        };

        // Disconnect should save session
        client.disconnect().await.unwrap();

        // Create new client that should load the saved session
        let new_client = WhatsAppClient::with_config(config).await.unwrap();

        // Should have the same session
        assert!(new_client.has_valid_session().await);
        let session_info = new_client.get_session_info().await.unwrap();
        assert_eq!(session_info.device_id, device_id);
        assert!(session_info.is_authenticated);
    }

    #[tokio::test]
    async fn test_client_connection_status() {
        let client = WhatsAppClient::new().await.unwrap();

        // Initially not connected
        assert!(!client.is_connected().await);

        // Connection attempt will fail with invalid URL, but we can test the status
        // The is_connected method should still work
        assert!(!client.is_connected().await);
    }

    #[tokio::test]
    async fn test_client_config_access() {
        let config = ClientConfig {
            server_url: "wss://test.example.com".to_string(),
            connect_timeout: Duration::from_secs(45),
            reconnect_attempts: 5,
            session_file_path: Some("test_session.json".to_string()),
        };

        let client = WhatsAppClient::with_config(config.clone()).await.unwrap();

        let client_config = client.config();
        assert_eq!(client_config.server_url, config.server_url);
        assert_eq!(client_config.connect_timeout, config.connect_timeout);
        assert_eq!(client_config.reconnect_attempts, config.reconnect_attempts);
        assert_eq!(client_config.session_file_path, config.session_file_path);
    }

    #[tokio::test]
    async fn test_client_builder_pattern() {
        use super::super::ClientBuilder;

        let client = ClientBuilder::new()
            .server_url("wss://custom.example.com")
            .connect_timeout(Duration::from_secs(60))
            .reconnect_attempts(10)
            .session_file("custom_session.json")
            .build()
            .await
            .unwrap();

        let config = client.config();
        assert_eq!(config.server_url, "wss://custom.example.com");
        assert_eq!(config.connect_timeout, Duration::from_secs(60));
        assert_eq!(config.reconnect_attempts, 10);
        assert_eq!(
            config.session_file_path,
            Some("custom_session.json".to_string())
        );
    }

    #[tokio::test]
    async fn test_session_info_structure() {
        let session_info = SessionInfo {
            device_id: "test_device".to_string(),
            is_authenticated: true,
            last_seen: chrono::Utc::now(),
            created_at: chrono::Utc::now() - chrono::Duration::hours(1),
            age_seconds: Some(3600),
            needs_refresh: false,
        };

        // Test Debug trait
        let debug_str = format!("{:?}", session_info);
        assert!(debug_str.contains("test_device"));
        assert!(debug_str.contains("true"));

        // Test Clone trait
        let cloned = session_info.clone();
        assert_eq!(cloned.device_id, session_info.device_id);
        assert_eq!(cloned.is_authenticated, session_info.is_authenticated);
        assert_eq!(cloned.needs_refresh, session_info.needs_refresh);
    }

    #[tokio::test]
    async fn test_client_with_invalid_session_file() {
        // Try to load from non-existent directory
        let config = ClientConfig {
            server_url: "wss://example.com".to_string(),
            connect_timeout: Duration::from_secs(30),
            reconnect_attempts: 3,
            session_file_path: Some("/nonexistent/path/session.json".to_string()),
        };

        // Should still create client successfully (just log warning)
        let client = WhatsAppClient::with_config(config).await.unwrap();

        // Should not have valid session
        assert!(!client.has_valid_session().await);
        assert!(client.needs_reauthentication().await);
    }

    #[tokio::test]
    async fn test_login_with_valid_session() {
        let mut client = WhatsAppClient::new().await.unwrap();

        // Set up valid session
        {
            let session_ref = client.session.clone();
            let mut session = session_ref.lock().await;
            session.set_authenticated("test_client".to_string(), "test_server".to_string());

            let keys = EncryptionKeys {
                noise_key: vec![1, 2, 3, 4],
                identity_key: vec![5, 6, 7, 8],
                signed_pre_key: vec![9, 10, 11, 12],
                registration_id: 12345,
            };
            session.set_encryption_keys(keys);
        }

        // Login should succeed immediately with valid session
        let result = client.login().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_login_without_session() {
        let mut client = WhatsAppClient::new().await.unwrap();

        // Initially no valid session
        assert!(!client.has_valid_session().await);

        // Login should complete the authentication flow
        // Note: This will fail to connect to the actual server, but we can test the flow
        let result = client.login().await;

        // The login will fail due to connection issues, but we can verify the attempt was made
        // In a real test environment, we would mock the WebSocket connection
        assert!(result.is_err());
    }

    #[tokio::test]
    async fn test_login_saves_session_to_file() {
        let temp_file = NamedTempFile::new().unwrap();
        let session_path = temp_file.path().to_string_lossy().to_string();

        let config = ClientConfig {
            server_url: "wss://example.com".to_string(),
            connect_timeout: Duration::from_secs(30),
            reconnect_attempts: 3,
            session_file_path: Some(session_path.clone()),
        };

        let mut client = WhatsAppClient::with_config(config).await.unwrap();

        // Initially no valid session
        assert!(!client.has_valid_session().await);

        // Login attempt (will fail due to connection, but we can test session handling)
        let result = client.login().await;

        // Even if login fails due to connection, the session file handling should work
        assert!(result.is_err());
    }
}
