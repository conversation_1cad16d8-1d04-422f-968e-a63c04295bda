//! Message factory for creating and validating messages

use crate::client::error_handling::ValidationHelper;
use crate::error::Result;
use crate::types::{Message, PhoneNumber};

/// Factory for creating validated messages
pub struct MessageFactory;

impl MessageFactory {
    /// Create a text message with validation
    pub fn create_text_message(to: PhoneNumber, content: &str) -> Result<Message> {
        ValidationHelper::validate_message_content(content)?;
        
        Ok(Message::Text {
            to,
            content: content.to_string(),
        })
    }

    /// Create an image message with validation
    pub fn create_image_message(
        to: PhoneNumber,
        image_data: Vec<u8>,
        caption: Option<String>,
        mime_type: String,
    ) -> Result<Message> {
        ValidationHelper::validate_image_data(&image_data, Some(&mime_type))?;
        
        if let Some(ref caption) = caption {
            ValidationHelper::validate_message_content(caption)?;
        }

        Ok(Message::Image {
            to,
            data: image_data,
            caption,
            mime_type,
        })
    }
}

/// Builder for complex message creation
pub struct MessageBuilder {
    to: Option<PhoneNumber>,
    content: Option<String>,
    image_data: Option<Vec<u8>>,
    caption: Option<String>,
    mime_type: Option<String>,
}

impl MessageBuilder {
    pub fn new() -> Self {
        Self {
            to: None,
            content: None,
            image_data: None,
            caption: None,
            mime_type: None,
        }
    }

    pub fn to(mut self, phone: PhoneNumber) -> Self {
        self.to = Some(phone);
        self
    }

    pub fn text(mut self, content: impl Into<String>) -> Self {
        self.content = Some(content.into());
        self
    }

    pub fn image(mut self, data: Vec<u8>, mime_type: impl Into<String>) -> Self {
        self.image_data = Some(data);
        self.mime_type = Some(mime_type.into());
        self
    }

    pub fn caption(mut self, caption: impl Into<String>) -> Self {
        self.caption = Some(caption.into());
        self
    }

    pub fn build(self) -> Result<Message> {
        let to = self.to.ok_or_else(|| {
            crate::error::WhatsAppError::InvalidInput("Recipient phone number is required".to_string())
        })?;

        match (self.content, self.image_data, self.mime_type) {
            (Some(content), None, None) => {
                MessageFactory::create_text_message(to, &content)
            }
            (_, Some(data), Some(mime_type)) => {
                MessageFactory::create_image_message(to, data, self.caption, mime_type)
            }
            _ => Err(crate::error::WhatsAppError::InvalidInput(
                "Message must have either text content or image data".to_string()
            ))
        }
    }
}

impl Default for MessageBuilder {
    fn default() -> Self {
        Self::new()
    }
}