# Features

## Optional Features

- `qr-terminal` - Display QR codes in terminal (enabled by default)
- `image-support` - Support for sending image messages
- `session-encryption` - Encrypt stored session files

## Configuration

```toml
[dependencies]
whatsmeow-rs = { version = "0.1", features = ["image-support"] }
```

## Feature Details

### QR Terminal Display
Displays QR codes directly in the terminal for easy scanning.

### Image Support
Enables sending image messages with captions.

### Session Encryption
Encrypts session files on disk for additional security.