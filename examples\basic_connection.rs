//! Basic WhatsApp connection and authentication example
//!
//! This example demonstrates how to:
//! - Create a WhatsApp client
//! - Connect to WhatsApp servers
//! - Authenticate using QR code
//! - Handle basic connection events
//!
//! Run with: cargo run --example basic_connection

use std::sync::Arc;
use tokio::signal;
use whatsmeow_rs::{Event, EventHandler, Result, WhatsAppClient, WhatsAppError};

/// Simple event handler that logs events to console
struct ConsoleEventHandler;

#[async_trait::async_trait]
impl EventHandler for ConsoleEventHandler {
    async fn handle_event(&self, event: Event) -> Result<()> {
        match event {
            Event::QRCodeGenerated(qr_data) => {
                println!("📱 QR Code generated! Scan with your WhatsApp mobile app:");
                println!("{:?}", qr_data);
                println!("Waiting for scan...");
            }
            Event::LoginSuccess => {
                println!("✅ Login successful! You are now connected to WhatsApp.");
            }
            Event::LoginFailure(reason) => {
                println!("❌ Login failed: {:?}", reason);
            }
            Event::ConnectionStatusChanged(status) => {
                println!("🔗 Connection status changed: {:?}", status);
            }
            Event::MessageReceived(msg) => {
                println!("📨 Message received from {}: {:?}", msg.from, msg.content);
            }
        }
        Ok(())
    }
}

#[tokio::main]
async fn main() -> Result<()> {
    // Initialize logging
    tracing_subscriber::fmt::init();

    println!("🚀 Starting WhatsApp client...");

    // Create client with default configuration
    let mut client = WhatsAppClient::new().await?;

    // Set up event handler
    let event_handler = Arc::new(ConsoleEventHandler);
    client.set_event_handler(event_handler);

    // Connect to WhatsApp servers
    println!("🔌 Connecting to WhatsApp servers...");
    match client.connect().await {
        Ok(_) => println!("✅ Connected successfully!"),
        Err(WhatsAppError::Connection(e)) => {
            eprintln!("❌ Connection failed: {}", e);
            return Err(WhatsAppError::Connection(e));
        }
        Err(e) => {
            eprintln!("❌ Unexpected error during connection: {}", e);
            return Err(e);
        }
    }

    // Authenticate with WhatsApp
    println!("🔐 Starting authentication...");
    match client.login().await {
        Ok(_) => {
            println!("🎉 Authentication completed successfully!");

            // Check session info
            if let Some(session_info) = client.get_session_info().await {
                println!("📋 Session Info:");
                println!("  Device ID: {}", session_info.device_id);
                println!("  Authenticated: {}", session_info.is_authenticated);
                println!("  Session age: {:?} seconds", session_info.age_seconds);
            }
        }
        Err(WhatsAppError::Authentication(msg)) => {
            eprintln!("❌ Authentication failed: {}", msg);
            return Err(WhatsAppError::Authentication(msg));
        }
        Err(e) => {
            eprintln!("❌ Unexpected error during authentication: {}", e);
            return Err(e);
        }
    }

    println!("✨ Client is ready! Press Ctrl+C to disconnect and exit.");

    // Wait for Ctrl+C
    match signal::ctrl_c().await {
        Ok(_) => {
            println!("\n🛑 Shutdown signal received. Disconnecting...");
        }
        Err(err) => {
            eprintln!("❌ Error listening for shutdown signal: {}", err);
        }
    }

    // Graceful shutdown
    match client.disconnect().await {
        Ok(_) => println!("👋 Disconnected successfully. Goodbye!"),
        Err(e) => eprintln!("⚠️  Error during disconnect: {}", e),
    }

    Ok(())
}
