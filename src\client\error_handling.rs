//! Centralized error handling utilities for client module

use crate::error::{Result, WhatsAppError};

/// Extension trait for Result to provide consistent error context
pub trait ResultExt<T> {
    /// Add context to an error with operation description
    fn with_context(self, context: &str) -> Result<T>;

    /// Add context with formatted message
    fn with_context_f(self, f: impl FnOnce() -> String) -> Result<T>;
}

impl<T, E> ResultExt<T> for std::result::Result<T, E>
where
    E: std::fmt::Display,
{
    fn with_context(self, context: &str) -> Result<T> {
        self.map_err(|e| WhatsAppError::Operation {
            operation: context.to_string(),
            context: "Operation failed".to_string(),
            source: Box::new(WhatsAppError::Generic(e.to_string())),
        })
    }

    fn with_context_f(self, f: impl FnOnce() -> String) -> Result<T> {
        self.map_err(|e| WhatsAppError::Operation {
            operation: f(),
            context: "Operation failed".to_string(),
            source: Box::new(WhatsAppError::Generic(e.to_string())),
        })
    }
}

/// Macro for consistent error logging and conversion
#[macro_export]
macro_rules! log_and_convert_error {
    ($result:expr, $operation:expr) => {
        $result.map_err(|e| {
            tracing::error!("Failed to {}: {}", $operation, e);
            e
        })
    };
    ($result:expr, $operation:expr, $level:ident) => {
        $result.map_err(|e| {
            tracing::$level!("Failed to {}: {}", $operation, e);
            e
        })
    };
}

/// Error context builder for more detailed error information
#[derive(Debug, Clone)]
pub struct ErrorContext {
    operation: String,
    details: Vec<String>,
    user_message: Option<String>,
}

/// Structured validation errors for better error handling
#[derive(Debug, Clone, PartialEq)]
pub enum ValidationError {
    PhoneNumber { reason: PhoneValidationReason },
    MessageContent { reason: MessageValidationReason },
    ImageData { reason: ImageValidationReason },
}

#[derive(Debug, Clone, PartialEq)]
pub enum PhoneValidationReason {
    Empty,
    MissingCountryCode,
    TooShort { min_length: usize },
    TooLong { max_length: usize },
    InvalidCharacters,
}

#[derive(Debug, Clone, PartialEq)]
pub enum MessageValidationReason {
    TooLong {
        max_length: usize,
        actual_length: usize,
    },
}

#[derive(Debug, Clone, PartialEq)]
pub enum ImageValidationReason {
    Empty,
    TooLarge {
        max_size: usize,
        actual_size: usize,
    },
    UnsupportedType {
        mime_type: String,
        supported_types: Vec<String>,
    },
}

impl ErrorContext {
    pub fn new(operation: impl Into<String>) -> Self {
        Self {
            operation: operation.into(),
            details: Vec::new(),
            user_message: None,
        }
    }

    pub fn with_detail(mut self, detail: impl Into<String>) -> Self {
        self.details.push(detail.into());
        self
    }

    pub fn with_user_message(mut self, message: impl Into<String>) -> Self {
        self.user_message = Some(message.into());
        self
    }

    pub fn build_error(self, source: WhatsAppError) -> WhatsAppError {
        let mut operation = self.operation;
        if !self.details.is_empty() {
            operation.push_str(&format!(" ({})", self.details.join(", ")));
        }

        WhatsAppError::Operation {
            operation,
            context: self.user_message.unwrap_or_else(|| "Operation failed".to_string()),
            source: Box::new(source),
        }
    }
}

/// Validation helper for common client operations
pub struct ValidationHelper;

impl ValidationHelper {
    /// Validate phone number format
    ///
    /// # Arguments
    /// * `phone` - Phone number string to validate
    ///
    /// # Returns
    /// * `Ok(())` if valid
    /// * `Err(WhatsAppError::InvalidInput)` if invalid
    pub fn validate_phone_number(phone: &str) -> Result<()> {
        if phone.is_empty() {
            return Err(WhatsAppError::InvalidInput(
                "Phone number cannot be empty".to_string(),
            ));
        }

        if !phone.starts_with('+') {
            return Err(WhatsAppError::InvalidInput(
                "Phone number must start with +".to_string(),
            ));
        }

        if phone.len() < 8 || phone.len() > 16 {
            return Err(WhatsAppError::InvalidInput(
                "Phone number must be between 8 and 16 characters (+ followed by 7-15 digits)"
                    .to_string(),
            ));
        }

        // Check that remaining characters are digits
        if !phone[1..].chars().all(|c| c.is_ascii_digit()) {
            return Err(WhatsAppError::InvalidInput(
                "Phone number must contain only digits after +".to_string(),
            ));
        }

        Ok(())
    }

    /// Validate message content
    ///
    /// # Arguments
    /// * `content` - Message content to validate
    ///
    /// # Returns
    /// * `Ok(())` if valid
    /// * `Err(WhatsAppError::InvalidInput)` if invalid
    pub fn validate_message_content(content: &str) -> Result<()> {
        const MAX_MESSAGE_LENGTH: usize = 65536;

        if content.len() > MAX_MESSAGE_LENGTH {
            return Err(WhatsAppError::InvalidInput(format!(
                "Message content too long (max {} characters)",
                MAX_MESSAGE_LENGTH
            )));
        }

        Ok(())
    }

    /// Validate image data with optional MIME type validation
    ///
    /// # Arguments
    /// * `data` - Image data bytes
    /// * `mime_type` - Optional MIME type of the image for validation
    ///
    /// # Returns
    /// * `Ok(())` if valid
    /// * `Err(WhatsAppError::InvalidInput)` if invalid
    pub fn validate_image_data(data: &[u8], mime_type: Option<&str>) -> Result<()> {
        const MAX_IMAGE_SIZE: usize = 16 * 1024 * 1024; // 16MB
        const SUPPORTED_MIME_TYPES: &[&str] =
            &["image/jpeg", "image/png", "image/gif", "image/webp"];

        if data.is_empty() {
            return Err(WhatsAppError::InvalidInput(
                "Image data cannot be empty".to_string(),
            ));
        }

        if data.len() > MAX_IMAGE_SIZE {
            return Err(WhatsAppError::InvalidInput(format!(
                "Image too large (max {} bytes)",
                MAX_IMAGE_SIZE
            )));
        }

        if let Some(mime_type) = mime_type {
            if !SUPPORTED_MIME_TYPES.contains(&mime_type) {
                return Err(WhatsAppError::InvalidInput(format!(
                    "Unsupported image type: {}. Supported types: {:?}",
                    mime_type, SUPPORTED_MIME_TYPES
                )));
            }
        }

        Ok(())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_validate_phone_number() {
        // Valid phone numbers
        assert!(ValidationHelper::validate_phone_number("+1234567890").is_ok());
        assert!(ValidationHelper::validate_phone_number("+12345678").is_ok());
        assert!(ValidationHelper::validate_phone_number("+123456789012345").is_ok());

        // Invalid phone numbers
        assert!(ValidationHelper::validate_phone_number("").is_err());
        assert!(ValidationHelper::validate_phone_number("1234567890").is_err()); // No +
        assert!(ValidationHelper::validate_phone_number("+123456").is_err()); // Too short
        assert!(ValidationHelper::validate_phone_number("+1234567890123456").is_err()); // Too long
        assert!(ValidationHelper::validate_phone_number("+123abc7890").is_err()); // Non-digits
    }

    #[test]
    fn test_validate_message_content() {
        // Valid content
        assert!(ValidationHelper::validate_message_content("Hello").is_ok());
        assert!(ValidationHelper::validate_message_content("").is_ok()); // Empty is allowed

        // Invalid content (too long)
        let long_message = "a".repeat(65537);
        assert!(ValidationHelper::validate_message_content(&long_message).is_err());
    }

    #[test]
    fn test_validate_image_data() {
        let valid_data = vec![0u8; 1024]; // 1KB image

        // Valid image with MIME type
        assert!(ValidationHelper::validate_image_data(&valid_data, Some("image/jpeg")).is_ok());
        assert!(ValidationHelper::validate_image_data(&valid_data, Some("image/png")).is_ok());

        // Valid image without MIME type validation
        assert!(ValidationHelper::validate_image_data(&valid_data, None).is_ok());

        // Invalid cases
        assert!(ValidationHelper::validate_image_data(&[], Some("image/jpeg")).is_err()); // Empty data
        assert!(ValidationHelper::validate_image_data(&valid_data, Some("image/bmp")).is_err()); // Unsupported type

        let large_data = vec![0u8; 17 * 1024 * 1024]; // 17MB
        assert!(ValidationHelper::validate_image_data(&large_data, Some("image/jpeg")).is_err()); // Too large
    }
}
