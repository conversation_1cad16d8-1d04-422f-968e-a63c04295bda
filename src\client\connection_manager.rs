//! Connection management for WhatsApp client

use crate::error::Result;
use crate::types::{ConnectionStatus, Event};
use std::sync::Arc;
use std::time::Duration;
use tokio::sync::{broadcast, Mutex};

/// Manages WebSocket connection lifecycle
pub struct ConnectionManager {
    connection: Arc<Mutex<crate::client::connection::Connection>>,
    state: Arc<Mutex<ConnectionState>>,
    event_sender: broadcast::Sender<Event>,
}

#[derive(Debug, Clone, PartialEq)]
pub enum ConnectionState {
    Disconnected,
    Connecting,
    Connected,
    Reconnecting,
    Failed,
}

impl ConnectionManager {
    pub fn new(
        connection: Arc<Mutex<crate::client::connection::Connection>>,
        event_sender: broadcast::Sender<Event>,
    ) -> Self {
        Self {
            connection,
            state: Arc::new(Mutex::new(ConnectionState::Disconnected)),
            event_sender,
        }
    }

    pub async fn connect(&self, timeout: Duration) -> Result<()> {
        self.update_state(ConnectionState::Connecting).await;

        let connect_future = async {
            let mut connection = self.connection.lock().await;
            connection.connect_with_session().await
        };

        match tokio::time::timeout(timeout, connect_future).await {
            Ok(Ok(_)) => {
                self.update_state(ConnectionState::Connected).await;
                self.emit_connection_event(ConnectionStatus::Connected)?;
                Ok(())
            }
            Ok(Err(e)) => {
                self.update_state(ConnectionState::Failed).await;
                Err(e)
            }
            Err(_) => {
                self.update_state(ConnectionState::Failed).await;
                Err(crate::error::WhatsAppError::Timeout { timeout })
            }
        }
    }

    pub async fn disconnect(&self) -> Result<()> {
        let mut connection = self.connection.lock().await;
        connection.close().await?;
        self.update_state(ConnectionState::Disconnected).await;
        self.emit_connection_event(ConnectionStatus::Disconnected)?;
        Ok(())
    }

    pub async fn is_connected(&self) -> bool {
        let connection = self.connection.lock().await;
        connection.is_connected()
    }

    pub async fn state(&self) -> ConnectionState {
        let state = self.state.lock().await;
        state.clone()
    }

    async fn update_state(&self, new_state: ConnectionState) {
        let mut state = self.state.lock().await;
        *state = new_state;
    }

    fn emit_connection_event(&self, status: ConnectionStatus) -> Result<()> {
        let event = Event::ConnectionStatusChanged(status);
        match self.event_sender.send(event) {
            Ok(_) => Ok(()),
            Err(_) => {
                tracing::debug!("No event receivers available");
                Ok(())
            }
        }
    }
}