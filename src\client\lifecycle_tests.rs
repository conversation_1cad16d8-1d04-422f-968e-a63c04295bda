//! Integration tests for client lifecycle management

#[cfg(test)]
mod tests {
    use super::super::*;
    use crate::client::test_utils::test_helpers::*;
    use crate::types::{ConnectionStatus, Event, PhoneNumber, QRCodeData};
    use futures;
    use std::sync::Arc;
    use std::time::Duration;
    use tokio::time::timeout;

    #[tokio::test]
    async fn test_client_creation_and_configuration() {
        // Test default client creation
        let client = create_test_client().await;
        assert!(!client.config().server_url.is_empty());

        // Test client creation with custom configuration
        let client = ClientBuilder::new()
            .server_url("ws://localhost:8080")
            .connect_timeout(Duration::from_secs(30))
            .reconnect_attempts(3)
            .session_file("test_session.json")
            .build()
            .await;

        assert!(client.is_ok());
        let client = client.unwrap();
        assert_eq!(client.config().server_url, "ws://localhost:8080");
        assert_eq!(client.config().connect_timeout, Duration::from_secs(30));
        assert_eq!(client.config().reconnect_attempts, 3);
        assert_eq!(
            client.config().session_file_path,
            Some("test_session.json".to_string())
        );
    }

    #[tokio::test]
    async fn test_event_handler_registration() {
        let mut client = create_test_client().await;
        let handler = MockEventHandler::new();
        let _handler_events = handler.events.clone();

        client.set_event_handler(Arc::new(handler));

        // Test event emission
        let test_event = Event::LoginSuccess;
        client.emit_event(test_event).unwrap();

        // Give some time for event processing
        tokio::time::sleep(Duration::from_millis(100)).await;

        // Note: Events are processed asynchronously, so we can't directly check
        // the handler's events here without starting event processing
    }

    #[tokio::test]
    async fn test_event_subscription() {
        let client = create_test_client().await;
        let mut event_receiver = client.subscribe_to_events();

        // Emit test events
        let test_events = vec![
            Event::LoginSuccess,
            Event::QRCodeGenerated(QRCodeData {
                data: "test_qr_data".to_string(),
                expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(300),
            }),
            Event::ConnectionStatusChanged(ConnectionStatus::Connected),
        ];

        for event in &test_events {
            client.emit_event(event.clone()).unwrap();
        }

        // Receive and verify events
        for _expected_event in test_events {
            let received_event = timeout(Duration::from_secs(1), event_receiver.recv()).await;
            assert!(received_event.is_ok());

            let received_event = received_event.unwrap();
            assert!(received_event.is_ok());

            // Note: We can't directly compare events due to their structure,
            // but we can verify they were received
        }
    }

    #[tokio::test]
    async fn test_message_listener_lifecycle() {
        let mut client = create_test_client().await;

        // Initially, no message listener should be running
        assert!(client.message_listener_handle.is_none());

        // Start message listener
        let result = client.start_message_listener().await;
        assert!(result.is_ok());
        assert!(client.message_listener_handle.is_some());

        // Starting again should be idempotent
        let result = client.start_message_listener().await;
        assert!(result.is_ok());

        // Stop message listener
        let result = client.stop_message_listener().await;
        assert!(result.is_ok());
        assert!(client.message_listener_handle.is_none());
    }

    #[tokio::test]
    async fn test_graceful_disconnect() {
        let mut client = create_test_client().await;
        let handler = MockEventHandler::new();
        let _handler_events = handler.events.clone();

        client.set_event_handler(Arc::new(handler));

        // Start message listener
        client.start_message_listener().await.unwrap();
        assert!(client.message_listener_handle.is_some());

        // Disconnect should stop all services gracefully
        let result = client.disconnect().await;
        assert!(result.is_ok());
        assert!(client.message_listener_handle.is_none());
    }

    #[tokio::test]
    async fn test_session_management_integration() {
        let test_future = async {
            // Create client with session file
            let session_file = "test_integration_session.json";
            let mut client = ClientBuilder::new()
                .session_file(session_file)
                .build()
                .await
                .unwrap();

            // Initially should not have valid session
            assert!(!client.has_valid_session().await);

            // Simulate session creation (in real scenario, this happens during login)
            {
                let mut session = client.session.lock().await;
                session.device_id = "test_device_123".to_string();
                session.is_authenticated = true;
                session.client_token = "test_client_token".to_string();
                session.server_token = "test_server_token".to_string();
                // Set encryption keys to make session valid
                session.encryption_keys = crate::client::session::EncryptionKeys {
                    noise_key: vec![1, 2, 3, 4],
                    identity_key: vec![5, 6, 7, 8],
                    signed_pre_key: vec![9, 10, 11, 12],
                    registration_id: 12345,
                };
            }

            // Now should have valid session
            assert!(client.has_valid_session().await);

            // Get session info
            let session_info = client.get_session_info().await;
            assert!(session_info.is_some());
            let session_info = session_info.unwrap();
            assert_eq!(session_info.device_id, "test_device_123");
            assert!(session_info.is_authenticated);

            // Disconnect should save session
            client.disconnect().await.unwrap();

            // Clean up test file
            let _ = std::fs::remove_file(session_file);
        };

        // Add timeout to prevent hanging
        timeout(Duration::from_secs(10), test_future).await.unwrap();
    }

    #[tokio::test]
    async fn test_error_handling_in_lifecycle() {
        let client = WhatsAppClient::new().await.unwrap();

        // Test sending message without authentication
        let phone = PhoneNumber::new("+1234567890").unwrap();
        let result = client.send_message(phone, "test message").await;
        assert!(result.is_err());

        if let Err(crate::error::WhatsAppError::Authentication(_)) = result {
            // Expected error type
        } else {
            panic!("Expected authentication error");
        }
    }

    #[tokio::test]
    async fn test_reconnection_with_session() {
        let mut client = WhatsAppClient::new().await.unwrap();

        // Set up a valid session
        {
            let mut session = client.session.lock().await;
            session.device_id = "test_device_reconnect".to_string();
            session.is_authenticated = true;
            session.client_token = "test_client_token".to_string();
            session.server_token = "test_server_token".to_string();
        }

        // Test reconnection (this will fail in test environment but should handle gracefully)
        let result = client.reconnect().await;
        // In test environment, this will likely fail due to no actual server
        // but the method should handle errors gracefully
        assert!(result.is_err() || result.is_ok());
    }

    #[tokio::test]
    async fn test_logout_functionality() {
        let test_future = async {
            let mut client = WhatsAppClient::new().await.unwrap();

            // Set up a valid session
            {
                let mut session = client.session.lock().await;
                session.device_id = "test_device_logout".to_string();
                session.is_authenticated = true;
                session.client_token = "test_client_token".to_string();
                session.server_token = "test_server_token".to_string();
                // Set encryption keys to make session valid
                session.encryption_keys = crate::client::session::EncryptionKeys {
                    noise_key: vec![1, 2, 3, 4],
                    identity_key: vec![5, 6, 7, 8],
                    signed_pre_key: vec![9, 10, 11, 12],
                    registration_id: 12345,
                };
            }

            assert!(client.has_valid_session().await);

            // Logout should clear session
            let result = client.logout().await;
            assert!(result.is_ok());

            // Session should no longer be valid
            assert!(!client.has_valid_session().await);
        };

        // Add timeout to prevent hanging
        timeout(Duration::from_secs(10), test_future).await.unwrap();
    }

    #[tokio::test]
    async fn test_full_client_lifecycle() {
        let test_future = async {
            let session_file = "test_full_lifecycle_session.json";
            let mut client = ClientBuilder::new()
                .session_file(session_file)
                .connect_timeout(Duration::from_secs(1))
                .reconnect_attempts(1)
                .build()
                .await
                .unwrap();

            let handler = MockEventHandler::new();
            let _handler_events = handler.events.clone();
            client.set_event_handler(Arc::new(handler));

            // 1. Initial state checks
            assert!(!client.is_connected().await);
            assert!(!client.has_valid_session().await);
            assert!(client.message_listener_handle.is_none());

            // 2. Connection attempt (will fail in test environment)
            let connect_result = client.connect().await;
            // In test environment, connection will fail, but we test the error handling
            assert!(connect_result.is_err());

            // 3. Test message listener can be started independently
            client.start_message_listener().await.unwrap();
            assert!(client.message_listener_handle.is_some());

            // 4. Test graceful shutdown
            client.disconnect().await.unwrap();
            assert!(client.message_listener_handle.is_none());

            // Clean up
            let _ = std::fs::remove_file(session_file);
        };

        // Add timeout to prevent hanging
        timeout(Duration::from_secs(15), test_future).await.unwrap();
    }

    #[tokio::test]
    async fn test_concurrent_operations() {
        let test_future = async {
            let mut client = WhatsAppClient::new().await.unwrap();
            let handler = MockEventHandler::new();
            client.set_event_handler(Arc::new(handler));

            // Start message listener
            client.start_message_listener().await.unwrap();

            // Emit multiple events concurrently
            let event_tasks = (0..10).map(|i| {
                let client_ref = &client;
                async move {
                    let event = Event::QRCodeGenerated(QRCodeData {
                        data: format!("qr_data_{}", i),
                        expires_at: std::time::SystemTime::now() + std::time::Duration::from_secs(300),
                    });
                    client_ref.emit_event(event)
                }
            });

            let results: Vec<_> = futures::future::join_all(event_tasks).await;

            // All events should be emitted successfully
            for result in results {
                assert!(result.is_ok());
            }

            // Clean shutdown
            client.disconnect().await.unwrap();
        };

        // Add timeout to prevent hanging
        timeout(Duration::from_secs(10), test_future).await.unwrap();
    }
}
