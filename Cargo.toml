[package]
name = "whatsmeow-rs"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1.0", features = ["full"] }
tokio-tungstenite = "0.27.0"
futures-util = "0.3"
prost = "0.14.1"
prost-types = "0.14.1"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
chrono = { version = "0.4", features = ["serde"] }
thiserror = "2.0.12"
async-trait = "0.1"
qrcode = "0.14"
snow = "0.9"
aes-gcm = "0.10"
sha2 = "0.10"
rand = "0.9.1"
base64 = "0.22.1"
pbkdf2 = "0.12"
hmac = "0.12.1"
url = "2.4"
tracing = "0.1"
bytes = "1.10.1"

[build-dependencies]
prost-build = "0.14.1"

[dev-dependencies]
tokio-test = "0.4"
mockall = "0.13.1"
tempfile = "3.8"
env_logger = "0.11.8"
futures = "0.3"
tracing-subscriber = "0.3"
